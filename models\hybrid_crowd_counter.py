"""
Hybrid Crowd Counter with Tracking-based Unique Person Counting
Combines frame-based detection with tracking for accurate unique person counting
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from .tracking_detector import TrackingPersonDetector
from .crowd_counter import SimpleCrowdCounter

logger = logging.getLogger(__name__)

class HybridCrowdCounter:
    """
    Advanced crowd counter that combines:
    1. Frame-based detection for instant crowd density
    2. Tracking-based counting for unique person counting
    3. CSRNet for dense crowd estimation
    """
    
    def __init__(self,
                 model_name: str = 'yolov11s',
                 detection_preset: str = 'balanced',
                 sahi_preset: str = 'balanced',
                 use_sahi: bool = True,
                 use_csrnet: bool = True,
                 tracking_max_age: int = 30,
                 tracking_min_hits: int = 3,
                 tracking_iou_threshold: float = 0.3):
        """
        Initialize hybrid crowd counter
        
        Args:
            model_name: YOLO model to use
            detection_preset: Detection configuration preset
            sahi_preset: SAHI configuration preset
            use_sahi: Whether to use SAHI for detection
            use_csrnet: Whether to use CSRNet for density estimation
            tracking_max_age: Maximum frames to keep track alive
            tracking_min_hits: Minimum hits before track initialization
            tracking_iou_threshold: IOU threshold for tracking
        """
        # Initialize tracking detector
        self.tracking_detector = TrackingPersonDetector(
            model_name=model_name,
            detection_preset=detection_preset,
            sahi_preset=sahi_preset,
            use_sahi=use_sahi,
            max_age=tracking_max_age,
            min_hits=tracking_min_hits,
            iou_threshold=tracking_iou_threshold
        )
        
        # Initialize traditional crowd counter for density analysis
        self.crowd_counter = SimpleCrowdCounter(use_csrnet=use_csrnet)
        
        # Video-level statistics
        self.video_stats = {
            'total_frames_processed': 0,
            'unique_persons_detected': 0,
            'max_simultaneous_persons': 0,
            'avg_persons_per_frame': 0.0,
            'total_person_detections': 0,
            'frame_results': []
        }
        
        # Line crossing detection (optional)
        self.line_crossing_enabled = False
        self.crossing_line = None
        self.persons_crossed = set()
        
        logger.info("Initialized HybridCrowdCounter with tracking capabilities")
    
    def load_models(self):
        """Load all required models"""
        self.tracking_detector.load_model()
        logger.info("All models loaded successfully")
    
    def set_crossing_line(self, line_coords: Tuple[Tuple[int, int], Tuple[int, int]]):
        """
        Set a line for counting people crossing
        
        Args:
            line_coords: ((x1, y1), (x2, y2)) coordinates of the crossing line
        """
        self.crossing_line = line_coords
        self.line_crossing_enabled = True
        self.persons_crossed = set()
        logger.info(f"Crossing line set: {line_coords}")
    
    def disable_line_crossing(self):
        """Disable line crossing detection"""
        self.line_crossing_enabled = False
        self.crossing_line = None
        self.persons_crossed = set()
    
    def _check_line_crossing(self, track_id: int, current_pos: List[int], 
                           previous_positions: List[List[int]]) -> bool:
        """
        Check if a track has crossed the defined line
        
        Args:
            track_id: Track ID
            current_pos: Current position [x, y]
            previous_positions: List of previous positions
            
        Returns:
            True if line was crossed in this frame
        """
        if not self.line_crossing_enabled or not self.crossing_line or len(previous_positions) < 2:
            return False
        
        # Get line coordinates
        (x1, y1), (x2, y2) = self.crossing_line
        
        # Check if the track crossed the line between previous and current position
        prev_pos = previous_positions[-2]  # Previous position
        
        # Simple line intersection check
        def ccw(A, B, C):
            return (C[1] - A[1]) * (B[0] - A[0]) > (B[1] - A[1]) * (C[0] - A[0])
        
        def intersect(A, B, C, D):
            return ccw(A, C, D) != ccw(B, C, D) and ccw(A, B, C) != ccw(A, B, D)
        
        crossed = intersect(prev_pos, current_pos, [x1, y1], [x2, y2])
        
        if crossed and track_id not in self.persons_crossed:
            self.persons_crossed.add(track_id)
            logger.info(f"Person {track_id} crossed the line")
            return True
        
        return False
    
    def process_frame(self, frame: np.ndarray, frame_number: int = 0, 
                     timestamp: float = 0.0) -> Dict:
        """
        Process a single frame with hybrid counting approach
        
        Args:
            frame: Input frame
            frame_number: Frame number in sequence
            timestamp: Timestamp of the frame
            
        Returns:
            Comprehensive analysis results
        """
        # Get tracking results
        tracking_result = self.tracking_detector.detect_and_track(frame)
        
        # Get traditional crowd analysis for density estimation
        crowd_analysis = self.crowd_counter.process_frame_with_density(
            frame, tracking_result['detections']
        )
        
        # Check line crossings if enabled
        new_crossings = 0
        if self.line_crossing_enabled:
            for det in tracking_result['tracked_detections']:
                track_id = det['track_id']
                current_pos = det['center']
                
                # Get track history
                if track_id in self.tracking_detector.track_history:
                    positions = self.tracking_detector.track_history[track_id]['positions']
                    if self._check_line_crossing(track_id, current_pos, positions):
                        new_crossings += 1
        
        # Update video-level statistics
        self.video_stats['total_frames_processed'] += 1
        self.video_stats['unique_persons_detected'] = tracking_result['unique_person_count']
        self.video_stats['max_simultaneous_persons'] = max(
            self.video_stats['max_simultaneous_persons'],
            tracking_result['tracked_count']
        )
        self.video_stats['total_person_detections'] += tracking_result['detection_count']
        self.video_stats['avg_persons_per_frame'] = (
            self.video_stats['total_person_detections'] / 
            self.video_stats['total_frames_processed']
        )
        
        # Create comprehensive frame result
        frame_result = {
            # Basic frame info
            'frame_number': frame_number,
            'timestamp': timestamp,
            
            # Detection counts
            'instant_detections': tracking_result['detection_count'],
            'tracked_persons': tracking_result['tracked_count'],
            'unique_persons_total': tracking_result['unique_person_count'],
            
            # Tracking information
            'active_track_ids': tracking_result['active_track_ids'],
            'detections': tracking_result['detections'],
            'tracked_detections': tracking_result['tracked_detections'],
            
            # Crowd density analysis
            'crowd_analysis': crowd_analysis['crowd_analysis'],
            'density_score': crowd_analysis['density_score'],
            'density_map': crowd_analysis['density_map'],
            
            # Line crossing (if enabled)
            'line_crossings': new_crossings if self.line_crossing_enabled else None,
            'total_crossings': len(self.persons_crossed) if self.line_crossing_enabled else None,
            
            # Hybrid counting result
            'recommended_count': self._get_recommended_count(tracking_result, crowd_analysis),
            'counting_method': self._get_counting_method(tracking_result, crowd_analysis)
        }
        
        # Store frame result
        self.video_stats['frame_results'].append(frame_result)
        
        return frame_result
    
    def _get_recommended_count(self, tracking_result: Dict, crowd_analysis: Dict) -> int:
        """
        Determine the most reliable count based on scene complexity
        
        Args:
            tracking_result: Results from tracking detector
            crowd_analysis: Results from crowd counter
            
        Returns:
            Recommended person count for this frame
        """
        detection_count = tracking_result['detection_count']
        tracked_count = tracking_result['tracked_count']
        density_count = crowd_analysis['crowd_analysis']['total_count']
        
        # For sparse scenes (<=10 people), trust tracking more
        if detection_count <= 10:
            return tracked_count
        
        # For medium density (11-30 people), use hybrid approach
        elif detection_count <= 30:
            return int(0.7 * tracked_count + 0.3 * density_count)
        
        # For dense scenes (>30 people), rely more on density estimation
        else:
            return int(0.3 * tracked_count + 0.7 * density_count)
    
    def _get_counting_method(self, tracking_result: Dict, crowd_analysis: Dict) -> str:
        """
        Determine which counting method was primarily used
        
        Args:
            tracking_result: Results from tracking detector
            crowd_analysis: Results from crowd counter
            
        Returns:
            String describing the primary counting method
        """
        detection_count = tracking_result['detection_count']
        
        if detection_count <= 10:
            return "tracking_based"
        elif detection_count <= 30:
            return "hybrid_tracking_density"
        else:
            return "density_based"
    
    def create_annotated_frame(self, frame: np.ndarray, frame_result: Dict) -> np.ndarray:
        """
        Create annotated frame with comprehensive information
        
        Args:
            frame: Original frame
            frame_result: Result from process_frame
            
        Returns:
            Annotated frame
        """
        # Start with tracking annotations
        annotated_frame = self.tracking_detector.annotate_frame_with_tracking(
            frame, {
                'tracked_detections': frame_result['tracked_detections'],
                'frame_count': frame_result['frame_number'],
                'detection_count': frame_result['instant_detections'],
                'tracked_count': frame_result['tracked_persons'],
                'unique_person_count': frame_result['unique_persons_total']
            }
        )
        
        # Add line crossing visualization if enabled
        if self.line_crossing_enabled and self.crossing_line:
            (x1, y1), (x2, y2) = self.crossing_line
            cv2.line(annotated_frame, (x1, y1), (x2, y2), (0, 255, 255), 3)
            cv2.putText(annotated_frame, "Crossing Line", (x1, y1 - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
        # Add comprehensive statistics
        stats_y = annotated_frame.shape[0] - 120
        stats_text = [
            f"Recommended Count: {frame_result['recommended_count']}",
            f"Method: {frame_result['counting_method']}",
            f"Density Score: {frame_result['density_score']:.3f}"
        ]
        
        if self.line_crossing_enabled:
            stats_text.append(f"Total Crossings: {frame_result['total_crossings']}")
        
        for i, text in enumerate(stats_text):
            cv2.putText(annotated_frame, text, (10, stats_y + i * 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        return annotated_frame
    
    def get_video_summary(self) -> Dict:
        """
        Get comprehensive video analysis summary
        
        Returns:
            Dictionary with video-level statistics and insights
        """
        tracking_stats = self.tracking_detector.get_tracking_statistics()
        
        # Calculate additional insights
        frame_counts = [fr['recommended_count'] for fr in self.video_stats['frame_results']]
        avg_count = np.mean(frame_counts) if frame_counts else 0
        max_count = max(frame_counts) if frame_counts else 0
        
        # Analyze counting methods used
        methods = [fr['counting_method'] for fr in self.video_stats['frame_results']]
        method_distribution = {method: methods.count(method) for method in set(methods)}
        
        summary = {
            # Core statistics
            'unique_persons_detected': self.video_stats['unique_persons_detected'],
            'total_frames_processed': self.video_stats['total_frames_processed'],
            'max_simultaneous_persons': self.video_stats['max_simultaneous_persons'],
            'average_persons_per_frame': avg_count,
            'peak_crowd_count': max_count,
            
            # Tracking statistics
            'tracking_statistics': tracking_stats,
            
            # Method analysis
            'counting_method_distribution': method_distribution,
            
            # Line crossing (if enabled)
            'total_line_crossings': len(self.persons_crossed) if self.line_crossing_enabled else None,
            'line_crossing_enabled': self.line_crossing_enabled,
            
            # Quality metrics
            'detection_reliability': self._calculate_detection_reliability(),
            'tracking_quality': self._calculate_tracking_quality()
        }
        
        return summary
    
    def _calculate_detection_reliability(self) -> float:
        """Calculate detection reliability score (0-1)"""
        if not self.video_stats['frame_results']:
            return 0.0
        
        # Based on consistency of detections and tracking success
        detection_variance = np.var([fr['instant_detections'] 
                                   for fr in self.video_stats['frame_results']])
        tracking_success = sum(1 for fr in self.video_stats['frame_results'] 
                             if fr['tracked_persons'] > 0) / len(self.video_stats['frame_results'])
        
        # Lower variance and higher tracking success = higher reliability
        reliability = min(1.0, tracking_success * (1.0 / (1.0 + detection_variance / 10.0)))
        return reliability
    
    def _calculate_tracking_quality(self) -> float:
        """Calculate tracking quality score (0-1)"""
        tracking_stats = self.tracking_detector.get_tracking_statistics()
        
        if tracking_stats['total_frames_processed'] == 0:
            return 0.0
        
        # Quality based on track duration and consistency
        avg_duration = tracking_stats['average_track_duration']
        detection_ratio = tracking_stats['avg_detections_per_frame']
        
        # Longer tracks and consistent detections = higher quality
        quality = min(1.0, (avg_duration / 10.0) * min(1.0, detection_ratio))
        return quality
    
    def reset(self):
        """Reset all counters and tracking state"""
        self.tracking_detector.reset_tracking()
        self.video_stats = {
            'total_frames_processed': 0,
            'unique_persons_detected': 0,
            'max_simultaneous_persons': 0,
            'avg_persons_per_frame': 0.0,
            'total_person_detections': 0,
            'frame_results': []
        }
        self.persons_crossed = set()
        logger.info("HybridCrowdCounter reset completed")
