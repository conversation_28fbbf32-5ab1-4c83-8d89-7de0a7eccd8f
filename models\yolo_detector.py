import cv2
import numpy as np
from ultralytics import YOLO
import os
from typing import List, Dict, Tuple, Optional
import logging
from sahi import AutoDetectionModel
from sahi.predict import get_sliced_prediction
from sahi.utils.cv import read_image
import sys

# Add parent directory to path for config import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import ModelConfig

logger = logging.getLogger(__name__)

class YOLOPersonDetector:
    """YOLO-based person detection for crowd counting with SAHI support and configurable parameters"""

    def __init__(self, model_name: Optional[str] = None, detection_preset: Optional[str] = None, sahi_preset: Optional[str] = None, use_sahi: bool = True):
        """
        Initialize YOLO detector with configurable parameters

        Args:
            model_name: YOLO model name (e.g., 'yolov11s', 'yolov8m'). If None, uses default from config
            detection_preset: Detection preset name (e.g., 'high_sensitivity', 'balanced'). If None, uses default
            sahi_preset: SAHI preset name (e.g., 'high_detail', 'balanced'). If None, uses default
            use_sahi: Whether to use SAHI for sliced inference (better for small objects)
        """
        # Get configuration
        self.yolo_config = ModelConfig.get_yolo_config(model_name, detection_preset)
        self.sahi_config = ModelConfig.get_sahi_config(sahi_preset) if use_sahi else None

        # Model settings
        self.model_path = self.yolo_config['model_path']
        self.model_info = self.yolo_config['model_info']
        self.model = None
        self.sahi_model = None
        self.use_sahi = use_sahi
        self.person_class_id = 0  # Person class ID in COCO dataset

        # Detection parameters from config
        self.confidence_threshold = self.yolo_config['confidence_threshold']
        self.nms_threshold = self.yolo_config['nms_threshold']
        self.max_detections = self.yolo_config.get('max_detections', 1000)  # Default max detections

        # SAHI parameters from config
        if self.sahi_config:
            self.slice_height = self.sahi_config['slice_height']
            self.slice_width = self.sahi_config['slice_width']
            self.overlap_height_ratio = self.sahi_config['overlap_height_ratio']
            self.overlap_width_ratio = self.sahi_config['overlap_width_ratio']
        else:
            # Default values if SAHI is disabled
            self.slice_height = 512
            self.slice_width = 512
            self.overlap_height_ratio = 0.2
            self.overlap_width_ratio = 0.2

        # Log configuration
        logger.info(f"YOLO Detector Configuration:")
        logger.info(f"  Model: {model_name or 'default'} ({self.model_path})")
        logger.info(f"  Detection preset: {detection_preset or 'default'}")
        logger.info(f"  Confidence threshold: {self.confidence_threshold}")
        logger.info(f"  NMS threshold: {self.nms_threshold}")
        if use_sahi:
            logger.info(f"  SAHI preset: {sahi_preset or 'default'}")
            logger.info(f"  SAHI slice size: {self.slice_width}x{self.slice_height}")
            logger.info(f"  SAHI overlap: {self.overlap_width_ratio}x{self.overlap_height_ratio}")

    def update_config(self, model_name: Optional[str] = None, detection_preset: Optional[str] = None, sahi_preset: Optional[str] = None):
        """Update detector configuration without reloading model"""
        if detection_preset:
            new_config = ModelConfig.get_yolo_config(model_name, detection_preset)
            self.confidence_threshold = new_config['confidence_threshold']
            self.nms_threshold = new_config['nms_threshold']
            logger.info(f"Updated detection preset to: {detection_preset}")

        if sahi_preset and self.use_sahi:
            new_sahi_config = ModelConfig.get_sahi_config(sahi_preset)
            self.slice_height = new_sahi_config['slice_height']
            self.slice_width = new_sahi_config['slice_width']
            self.overlap_height_ratio = new_sahi_config['overlap_height_ratio']
            self.overlap_width_ratio = new_sahi_config['overlap_width_ratio']
            logger.info(f"Updated SAHI preset to: {sahi_preset}")

            # Reinitialize SAHI model with new parameters
            if self.model is not None:
                self._initialize_sahi_model()

    def update_sahi_params(self, **params):
        """Update SAHI parameters directly"""
        if not self.use_sahi:
            logger.warning("SAHI is not enabled, cannot update SAHI parameters")
            return

        updated = False
        if 'slice_height' in params:
            self.slice_height = params['slice_height']
            updated = True
        if 'slice_width' in params:
            self.slice_width = params['slice_width']
            updated = True
        if 'overlap_height_ratio' in params:
            self.overlap_height_ratio = params['overlap_height_ratio']
            updated = True
        if 'overlap_width_ratio' in params:
            self.overlap_width_ratio = params['overlap_width_ratio']
            updated = True

        if updated:
            logger.info(f"Updated SAHI parameters: {params}")
            # Reinitialize SAHI model with new parameters
            if self.model is not None:
                self._initialize_sahi_model()

    def get_config_info(self) -> Dict:
        """Get current configuration information"""
        return {
            'model_path': self.model_path,
            'model_info': self.model_info,
            'confidence_threshold': self.confidence_threshold,
            'nms_threshold': self.nms_threshold,
            'use_sahi': self.use_sahi,
            'sahi_config': self.sahi_config,
            'slice_size': f"{self.slice_width}x{self.slice_height}",
            'overlap_ratio': f"{self.overlap_width_ratio}x{self.overlap_height_ratio}"
        }
        
    def load_model(self):
        """Load the YOLO model and SAHI model if enabled"""
        try:
            logger.info(f"Loading YOLO model: {self.model_path}")
            logger.info(f"Model info: {self.model_info['description']}")
            self.model = YOLO(self.model_path)
            logger.info("YOLO model loaded successfully")

            if self.use_sahi:
                self._initialize_sahi_model()

        except Exception as e:
            logger.error(f"Failed to load YOLO model: {e}")
            raise

    def _initialize_sahi_model(self):
        """Initialize SAHI model with current configuration"""
        try:
            logger.info("Initializing SAHI model...")

            # Determine model type based on model path
            model_type = 'yolov8'
            if 'yolo11' in self.model_path or 'yolov11' in self.model_path:
                model_type = 'yolov8'  # YOLOv11 uses same interface as YOLOv8

            self.sahi_model = AutoDetectionModel.from_pretrained(
                model_type=model_type,
                model_path=self.model_path,
                confidence_threshold=self.confidence_threshold,
                device='cpu'  # Use CPU for compatibility
            )
            logger.info(f"SAHI model initialized successfully with {model_type}")
            logger.info(f"SAHI slice configuration: {self.slice_width}x{self.slice_height}, overlap: {self.overlap_width_ratio}x{self.overlap_height_ratio}")

        except Exception as e:
            logger.error(f"Failed to initialize SAHI model: {e}")
            logger.warning("Falling back to standard YOLO detection")
            self.sahi_model = None
            self.use_sahi = False
    
    def detect_persons(self, image: np.ndarray) -> List[Dict]:
        """
        Detect persons in an image using YOLO or SAHI

        Args:
            image: Input image as numpy array

        Returns:
            List of detection dictionaries with bbox, confidence, etc.
        """
        if self.model is None:
            self.load_model()

        try:
            if self.use_sahi and self.sahi_model is not None:
                return self._detect_with_sahi(image)
            else:
                return self._detect_with_yolo(image)

        except Exception as e:
            logger.error(f"Error during person detection: {e}")
            return []

    def _detect_with_yolo(self, image: np.ndarray) -> List[Dict]:
        """Standard YOLO detection"""
        results = self.model(image, conf=self.confidence_threshold, iou=self.nms_threshold)

        detections = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Check if detection is a person (class 0 in COCO)
                    class_id = int(box.cls[0])
                    if class_id == self.person_class_id:
                        # Get bounding box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = float(box.conf[0])

                        detections.append({
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': confidence,
                            'class_id': class_id,
                            'class_name': 'person',
                            'center': [int((x1 + x2) / 2), int((y1 + y2) / 2)],
                            'area': int((x2 - x1) * (y2 - y1)),
                            'method': 'yolo'
                        })

        logger.debug(f"YOLO detected {len(detections)} persons")
        return detections

    def _detect_with_sahi(self, image: np.ndarray) -> List[Dict]:
        """SAHI sliced inference detection"""
        # Get sliced prediction
        result = get_sliced_prediction(
            image,
            self.sahi_model,
            slice_height=self.slice_height,
            slice_width=self.slice_width,
            overlap_height_ratio=self.overlap_height_ratio,
            overlap_width_ratio=self.overlap_width_ratio
        )

        detections = []
        for object_prediction in result.object_prediction_list:
            # Check if detection is a person
            if object_prediction.category.name == 'person':
                bbox = object_prediction.bbox.to_xyxy()
                confidence = object_prediction.score.value

                detections.append({
                    'bbox': [int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])],
                    'confidence': confidence,
                    'class_id': self.person_class_id,
                    'class_name': 'person',
                    'center': [int((bbox[0] + bbox[2]) / 2), int((bbox[1] + bbox[3]) / 2)],
                    'area': int((bbox[2] - bbox[0]) * (bbox[3] - bbox[1])),
                    'method': 'sahi'
                })

        logger.debug(f"SAHI detected {len(detections)} persons")
        return detections
    
    def annotate_image(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        Draw bounding boxes and labels on the image
        
        Args:
            image: Input image
            detections: List of detection dictionaries
            
        Returns:
            Annotated image
        """
        annotated_image = image.copy()
        
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            
            # Draw bounding box
            cv2.rectangle(
                annotated_image,
                (bbox[0], bbox[1]),
                (bbox[2], bbox[3]),
                (0, 255, 0),  # Green color
                2
            )
            
            # Draw label
            label = f"Person: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            
            # Draw label background
            cv2.rectangle(
                annotated_image,
                (bbox[0], bbox[1] - label_size[1] - 10),
                (bbox[0] + label_size[0], bbox[1]),
                (0, 255, 0),
                -1
            )
            
            # Draw label text
            cv2.putText(
                annotated_image,
                label,
                (bbox[0], bbox[1] - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (0, 0, 0),  # Black text
                2
            )
        
        # Add count text
        count_text = f"Persons detected: {len(detections)}"
        cv2.putText(
            annotated_image,
            count_text,
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            1,
            (0, 0, 255),  # Red text
            2
        )
        
        return annotated_image
    
    def process_frame(self, frame: np.ndarray, save_path: str = None) -> Tuple[List[Dict], np.ndarray]:
        """
        Process a single frame: detect persons and create annotated image
        
        Args:
            frame: Input frame
            save_path: Optional path to save annotated frame
            
        Returns:
            Tuple of (detections, annotated_frame)
        """
        # Detect persons
        detections = self.detect_persons(frame)
        
        # Create annotated image
        annotated_frame = self.annotate_image(frame, detections)
        
        # Save annotated frame if path provided
        if save_path:
            cv2.imwrite(save_path, annotated_frame)
            logger.debug(f"Saved annotated frame to {save_path}")
        
        return detections, annotated_frame
    
    def get_model_info(self) -> Dict:
        """Get information about the loaded model"""
        if self.model is None:
            return {"status": "not_loaded"}

        info = {
            "status": "loaded",
            "model_path": self.model_path,
            "model_type": "YOLOv8",
            "confidence_threshold": self.confidence_threshold,
            "nms_threshold": self.nms_threshold,
            "target_class": "person",
            "sahi_enabled": self.use_sahi
        }

        if self.use_sahi:
            info.update({
                "sahi_slice_height": self.slice_height,
                "sahi_slice_width": self.slice_width,
                "sahi_overlap_height_ratio": self.overlap_height_ratio,
                "sahi_overlap_width_ratio": self.overlap_width_ratio,
                "inference_method": "SAHI (Sliced Aided Hyper Inference)"
            })
        else:
            info["inference_method"] = "Standard YOLO"

        return info
