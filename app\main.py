from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi import Request
import cv2
import numpy as np
import os
import tempfile
import requests
from typing import Optional, List
import json
from datetime import datetime
import logging
import sys

# Add models directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from models.yolo_detector import YOLOPersonDetector
from models.crowd_counter import SimpleCrowdCounter
from models.hybrid_crowd_counter import HybridCrowdCounter
from config import ModelConfig

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="AI Crowd POC - Person Counting API",
    description="A proof of concept for video-based person counting and crowd analysis",
    version="1.0.0"
)

# CORS configuration for testing
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates for web UI
templates = Jinja2Templates(directory="templates")

# Global variables for models
yolo_detector = None
crowd_counter = None
hybrid_counter = None

@app.on_event("startup")
async def startup_event():
    """Initialize models on startup"""
    global yolo_detector, crowd_counter, hybrid_counter

    logger.info("Starting up AI Crowd POC application...")

    try:
        # Initialize YOLO detector with optimized configuration for Shibuya-like scenarios
        logger.info("Initializing YOLO person detector with optimized configuration...")
        yolo_detector = YOLOPersonDetector(
            model_name='yolov11s',  # Better model than yolov8n
            detection_preset='shibuya_optimized',  # Lower confidence threshold for dense crowds
            sahi_preset='high_detail',  # Better small object detection
            use_sahi=True
        )
        yolo_detector.load_model()

        # Initialize crowd counter with CSRNet
        logger.info("Initializing crowd counter with CSRNet...")
        crowd_counter = SimpleCrowdCounter(use_csrnet=True)

        # Initialize hybrid crowd counter with tracking
        logger.info("Initializing hybrid crowd counter with tracking...")
        hybrid_counter = HybridCrowdCounter(
            model_name='yolov11s',
            detection_preset='shibuya_optimized',
            sahi_preset='high_detail',
            use_sahi=True,
            use_csrnet=True,
            tracking_max_age=30,
            tracking_min_hits=3,
            tracking_iou_threshold=0.3
        )
        hybrid_counter.load_models()

        logger.info("All models initialized successfully!")

    except Exception as e:
        logger.error(f"Failed to initialize models: {e}")
        # Continue without models for basic functionality

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Serve the main web interface"""
    return templates.TemplateResponse(request, "index.html")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/models/info")
async def get_models_info():
    """Get detailed information about loaded models"""
    global yolo_detector, crowd_counter

    info = {
        "timestamp": datetime.now().isoformat(),
        "models": {}
    }

    if yolo_detector:
        info["models"]["yolo"] = yolo_detector.get_model_info()
    else:
        info["models"]["yolo"] = {"status": "not_loaded"}

    if crowd_counter:
        info["models"]["crowd_counter"] = crowd_counter.get_model_info()
    else:
        info["models"]["crowd_counter"] = {"status": "not_loaded"}

    return info

@app.post("/analyze-video")
async def analyze_video(
    video_file: Optional[UploadFile] = File(None),
    video_url: Optional[str] = Form(None)
):
    """
    Analyze video for person counting
    Accepts either uploaded file or video URL
    """
    if not video_file and not video_url:
        raise HTTPException(status_code=400, detail="Either video file or video URL must be provided")
    
    if video_file and video_url:
        raise HTTPException(status_code=400, detail="Provide either video file or video URL, not both")
    
    try:
        # Process video and extract frames
        frames_data = await process_video(video_file, video_url)
        
        # Analyze frames with tracking-based counting
        results = await analyze_frames_with_tracking(frames_data)
        
        return JSONResponse(content=results)
        
    except Exception as e:
        logger.error(f"Error analyzing video: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing video: {str(e)}")

async def process_video(video_file: Optional[UploadFile], video_url: Optional[str]):
    """
    Process video and extract frames at 1 FPS
    """
    temp_video_path = None
    
    try:
        if video_file:
            # Save uploaded file temporarily
            temp_video_path = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{video_file.filename}"
            with open(temp_video_path, "wb") as buffer:
                content = await video_file.read()
                buffer.write(content)
            video_path = temp_video_path
        else:
            # Download video from URL
            response = requests.get(video_url, stream=True)
            response.raise_for_status()
            temp_video_path = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_url_video.mp4"
            with open(temp_video_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            video_path = temp_video_path
        
        # Extract frames using OpenCV
        frames_data = extract_frames(video_path)
        
        return frames_data
        
    finally:
        # Clean up temporary file
        if temp_video_path and os.path.exists(temp_video_path):
            os.remove(temp_video_path)

def extract_frames(video_path: str, fps: int = 1):
    """
    Extract frames from video at specified FPS
    """
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        raise ValueError("Could not open video file")
    
    # Get video properties
    original_fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / original_fps
    
    logger.info(f"Video info: {original_fps} FPS, {total_frames} frames, {duration:.2f} seconds")
    
    frames_data = []
    frame_interval = int(original_fps / fps)  # Extract every N frames for 1 FPS
    frame_count = 0
    extracted_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        if frame_count % frame_interval == 0:
            # Save frame to static directory
            frame_filename = f"frame_{extracted_count}.jpg"
            frame_path = os.path.join("static", frame_filename)
            cv2.imwrite(frame_path, frame)
            
            frames_data.append({
                "frame_number": extracted_count,
                "timestamp": frame_count / original_fps,
                "frame_path": frame_path,
                "frame_url": f"/static/{frame_filename}"
            })
            extracted_count += 1
        
        frame_count += 1
    
    cap.release()
    logger.info(f"Extracted {extracted_count} frames from video")
    
    return frames_data

async def analyze_frames_with_tracking(frames_data: List[dict]):
    """
    Analyze extracted frames using hybrid tracking-based counting
    """
    global hybrid_counter

    if not hybrid_counter:
        logger.warning("Hybrid counter not initialized, falling back to traditional analysis")
        return await analyze_frames(frames_data)

    try:
        # Reset tracking state for new video
        hybrid_counter.reset()

        frame_results = []
        annotated_frame_urls = []

        logger.info(f"Analyzing {len(frames_data)} frames with tracking...")

        for i, frame_info in enumerate(frames_data):
            frame_path = frame_info["frame_path"]
            timestamp = frame_info.get("timestamp", i * 1.0)

            # Load frame
            frame = cv2.imread(frame_path)
            if frame is None:
                logger.warning(f"Could not load frame: {frame_path}")
                continue

            # Process frame with hybrid counter
            frame_result = hybrid_counter.process_frame(frame, i, timestamp)

            # Create annotated frame
            annotated_frame = hybrid_counter.create_annotated_frame(frame, frame_result)

            # Save annotated frame
            annotated_filename = f"tracked_frame_{i}.jpg"
            annotated_path = os.path.join("static", annotated_filename)
            cv2.imwrite(annotated_path, annotated_frame)

            # Store results
            frame_results.append({
                "frame_number": i,
                "timestamp": timestamp,
                "instant_detections": frame_result['instant_detections'],
                "tracked_persons": frame_result['tracked_persons'],
                "unique_persons_total": frame_result['unique_persons_total'],
                "recommended_count": frame_result['recommended_count'],
                "counting_method": frame_result['counting_method'],
                "density_score": frame_result['density_score'],
                "annotated_frame_url": f"/static/{annotated_filename}",
                "active_track_ids": frame_result['active_track_ids']
            })
            annotated_frame_urls.append(f"/static/{annotated_filename}")

            if (i + 1) % 10 == 0:
                logger.info(f"Processed {i + 1}/{len(frames_data)} frames with tracking")

        # Get video summary
        video_summary = hybrid_counter.get_video_summary()

        logger.info(f"Tracking analysis completed: {video_summary['unique_persons_detected']} unique persons detected")

        return {
            "frame_results": frame_results,
            "annotated_frame_urls": annotated_frame_urls,
            "video_summary": video_summary,
            "analysis_type": "tracking_based"
        }

    except Exception as e:
        logger.error(f"Error in tracking analysis: {e}")
        # Fallback to traditional analysis
        return await analyze_frames(frames_data)


async def analyze_frames(frames_data: List[dict]):
    """
    Analyze extracted frames for person counting using YOLO and crowd counting models (traditional approach)
    """
    global yolo_detector, crowd_counter

    if not yolo_detector or not crowd_counter:
        logger.warning("Models not initialized, using placeholder results")
        return await analyze_frames_placeholder(frames_data)

    try:
        total_detections = 0
        frame_results = []
        annotated_frame_urls = []

        logger.info(f"Analyzing {len(frames_data)} frames...")

        for i, frame_info in enumerate(frames_data):
            frame_path = frame_info["frame_path"]

            # Load frame
            frame = cv2.imread(frame_path)
            if frame is None:
                logger.warning(f"Could not load frame: {frame_path}")
                continue

            # Detect persons using YOLO
            detections = yolo_detector.detect_persons(frame)

            # Analyze crowd density
            crowd_analysis = crowd_counter.process_frame_with_density(frame, detections)

            # Create annotated frame
            annotated_frame = yolo_detector.annotate_image(frame, detections)

            # Save annotated frame
            annotated_filename = f"annotated_frame_{i}.jpg"
            annotated_path = os.path.join("static", annotated_filename)
            cv2.imwrite(annotated_path, annotated_frame)

            # Store results
            frame_result = {
                "frame_number": i,
                "timestamp": frame_info["timestamp"],
                "detections_count": len(detections),
                "detections": detections,
                "crowd_analysis": crowd_analysis["crowd_analysis"],
                "density_score": crowd_analysis["density_score"],
                "annotated_frame_url": f"/static/{annotated_filename}"
            }

            frame_results.append(frame_result)
            total_detections += len(detections)

            # Keep first 3 annotated frames for display
            if len(annotated_frame_urls) < 3:
                annotated_frame_urls.append(f"/static/{annotated_filename}")

            if (i + 1) % 5 == 0:
                logger.info(f"Processed {i + 1}/{len(frames_data)} frames")

        # Calculate final statistics
        average_count = total_detections / len(frames_data) if frames_data else 0

        results = {
            "total_estimated_count": total_detections,
            "average_count_per_frame": average_count,
            "total_frames_analyzed": len(frames_data),
            "annotated_frames": annotated_frame_urls,
            "details": {
                "frames": frame_results,
                "processing_timestamp": datetime.now().isoformat(),
                "model_versions": {
                    "yolo": yolo_detector.get_model_info() if yolo_detector else "Not available",
                    "crowd_counting": crowd_counter.get_model_info() if crowd_counter else "Not available"
                },
                "summary_statistics": {
                    "max_count_per_frame": max([fr["detections_count"] for fr in frame_results]) if frame_results else 0,
                    "min_count_per_frame": min([fr["detections_count"] for fr in frame_results]) if frame_results else 0,
                    "avg_density_score": np.mean([fr["density_score"] for fr in frame_results]) if frame_results else 0
                }
            }
        }

        logger.info(f"Analysis complete: {total_detections} total detections across {len(frames_data)} frames")
        return results

    except Exception as e:
        logger.error(f"Error during frame analysis: {e}")
        return await analyze_frames_placeholder(frames_data)

async def analyze_frames_placeholder(frames_data: List[dict]):
    """Fallback placeholder implementation"""
    total_estimated_count = len(frames_data) * 3  # Conservative estimate

    results = {
        "total_estimated_count": total_estimated_count,
        "average_count_per_frame": total_estimated_count / len(frames_data) if frames_data else 0,
        "total_frames_analyzed": len(frames_data),
        "annotated_frames": [frame["frame_url"] for frame in frames_data[:3]],
        "details": {
            "frames": frames_data,
            "processing_timestamp": datetime.now().isoformat(),
            "model_versions": {
                "yolo": "Placeholder mode - models not loaded",
                "crowd_counting": "Placeholder mode - models not loaded"
            },
            "note": "Using placeholder analysis - models failed to initialize"
        }
    }

    return results

@app.get("/config/models")
async def get_available_models():
    """Get list of available YOLO models and their descriptions"""
    return {
        "yolo_models": ModelConfig.list_available_models(),
        "detection_presets": ModelConfig.list_detection_presets(),
        "sahi_presets": ModelConfig.list_sahi_presets()
    }

@app.get("/config/scenarios")
async def get_scenario_configs():
    """Get recommended configurations for different scenarios"""
    scenarios = ['shibuya_crossing', 'indoor_crowd', 'outdoor_event', 'security_monitoring', 'real_time']
    configs = {}
    for scenario in scenarios:
        try:
            configs[scenario] = ModelConfig.get_recommended_config_for_scenario(scenario)
        except ValueError:
            continue
    return configs

@app.post("/config/update")
async def update_detector_config(
    model_name: Optional[str] = None,
    detection_preset: Optional[str] = None,
    sahi_preset: Optional[str] = None,
    confidence_threshold: Optional[float] = None,
    nms_threshold: Optional[float] = None,
    max_detections: Optional[int] = None,
    slice_height: Optional[int] = None,
    slice_width: Optional[int] = None,
    overlap_height_ratio: Optional[float] = None,
    overlap_width_ratio: Optional[float] = None
):
    """Update detector configuration without restarting"""
    global yolo_detector

    if not yolo_detector:
        raise HTTPException(status_code=400, detail="YOLO detector not initialized")

    try:
        # Update basic configuration
        yolo_detector.update_config(model_name, detection_preset, sahi_preset)

        # Update advanced parameters if provided
        if confidence_threshold is not None:
            yolo_detector.confidence_threshold = confidence_threshold
        if nms_threshold is not None:
            yolo_detector.nms_threshold = nms_threshold
        if max_detections is not None:
            yolo_detector.max_detections = max_detections

        # Update SAHI parameters if provided
        if any([slice_height, slice_width, overlap_height_ratio, overlap_width_ratio]):
            sahi_params = {}
            if slice_height is not None:
                sahi_params['slice_height'] = slice_height
            if slice_width is not None:
                sahi_params['slice_width'] = slice_width
            if overlap_height_ratio is not None:
                sahi_params['overlap_height_ratio'] = overlap_height_ratio
            if overlap_width_ratio is not None:
                sahi_params['overlap_width_ratio'] = overlap_width_ratio

            # Update SAHI configuration
            yolo_detector.update_sahi_params(**sahi_params)

        return {
            "status": "success",
            "message": "Configuration updated successfully",
            "current_config": yolo_detector.get_config_info()
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to update configuration: {e}")

@app.get("/config/current")
async def get_current_config():
    """Get current detector configuration"""
    global yolo_detector, crowd_counter, hybrid_counter

    if not yolo_detector:
        return {"status": "not_initialized", "message": "YOLO detector not initialized"}

    config_info = yolo_detector.get_config_info()
    config_info['crowd_counter_enabled'] = crowd_counter is not None
    config_info['tracking_enabled'] = hybrid_counter is not None

    if hybrid_counter:
        tracking_config = hybrid_counter.tracking_detector.get_config_info()
        config_info['tracking_config'] = tracking_config

    return {
        "status": "initialized",
        "config": config_info
    }

@app.get("/analysis/comparison")
async def compare_analysis_methods():
    """Compare traditional vs tracking-based analysis methods"""
    return {
        "methods": {
            "traditional": {
                "name": "Frame-based Detection",
                "description": "Counts persons in each frame independently",
                "pros": ["Fast processing", "Good for instant crowd density"],
                "cons": ["Counts same person multiple times", "Not suitable for unique person counting"],
                "use_cases": ["Real-time monitoring", "Instant crowd density"]
            },
            "tracking": {
                "name": "Tracking-based Counting",
                "description": "Tracks persons across frames for unique counting",
                "pros": ["Unique person counting", "Accurate for long videos", "Eliminates double counting"],
                "cons": ["More computationally intensive", "May lose tracks in dense crowds"],
                "use_cases": ["Total visitor counting", "Long video analysis", "Accurate statistics"]
            },
            "hybrid": {
                "name": "Hybrid Approach",
                "description": "Combines detection and tracking based on scene complexity",
                "pros": ["Best of both methods", "Adaptive to crowd density", "Most accurate"],
                "cons": ["Most complex", "Requires more resources"],
                "use_cases": ["Professional analysis", "Research applications", "High accuracy requirements"]
            }
        },
        "current_method": "hybrid",
        "recommendation": "Hybrid approach is recommended for most accurate results"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
