#!/usr/bin/env python3
"""
Test script for configuration optimization
Tests different YOLO models and detection presets to find optimal settings for Shibuya video
"""

import requests
import json
import os
import time
from typing import Dict, List

def test_configuration(model_name: str, detection_preset: str, sahi_preset: str) -> Dict:
    """Test a specific configuration"""
    
    print(f"\n🧪 Testing Configuration:")
    print(f"   Model: {model_name}")
    print(f"   Detection: {detection_preset}")
    print(f"   SAHI: {sahi_preset}")
    print("-" * 50)
    
    # Update configuration
    try:
        config_response = requests.post(
            "http://localhost:8000/config/update",
            params={
                "model_name": model_name,
                "detection_preset": detection_preset,
                "sahi_preset": sahi_preset
            },
            timeout=30
        )
        
        if config_response.status_code != 200:
            print(f"❌ Failed to update configuration: {config_response.text}")
            return None
            
        print("✅ Configuration updated successfully")
        
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return None
    
    # Test with Shibuya video
    shibuya_path = r"C:\Users\<USER>\Downloads\Shibuya scramble crossing tokyo pedestrian travel japan shibuya shibuyascramble.mp4"
    
    if not os.path.exists(shibuya_path):
        print(f"❌ Shibuya video not found: {shibuya_path}")
        return None
    
    try:
        start_time = time.time()
        
        with open(shibuya_path, 'rb') as video_file:
            files = {
                'video_file': (os.path.basename(shibuya_path), video_file, 'video/mp4')
            }
            
            response = requests.post(
                "http://localhost:8000/analyze-video", 
                files=files,
                timeout=300  # 5 minute timeout
            )
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            total_detections = result.get('total_estimated_count', 0)
            avg_count = result.get('average_count_per_frame', 0)
            frames_analyzed = result.get('total_frames_analyzed', 0)
            
            print(f"✅ Analysis completed in {processing_time:.1f}s")
            print(f"📊 Results:")
            print(f"   Total detections: {total_detections}")
            print(f"   Average per frame: {avg_count:.2f}")
            print(f"   Frames analyzed: {frames_analyzed}")
            print(f"   Processing speed: {frames_analyzed/processing_time:.2f} FPS")
            
            return {
                'model_name': model_name,
                'detection_preset': detection_preset,
                'sahi_preset': sahi_preset,
                'total_detections': total_detections,
                'avg_count_per_frame': avg_count,
                'frames_analyzed': frames_analyzed,
                'processing_time': processing_time,
                'fps': frames_analyzed/processing_time,
                'success': True
            }
        else:
            print(f"❌ Analysis failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        return None

def run_optimization_tests():
    """Run comprehensive optimization tests"""
    
    print("🚀 AI Crowd POC - Configuration Optimization Tests")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly.")
            return
    except:
        print("❌ Cannot connect to server. Please make sure it's running.")
        return
    
    # Test configurations (ordered from most aggressive to balanced)
    test_configs = [
        # Most aggressive - maximum detection
        {
            'model_name': 'yolov11m',
            'detection_preset': 'shibuya_optimized',
            'sahi_preset': 'maximum_detection',
            'description': 'Maximum detection capability'
        },
        # High sensitivity with detailed SAHI
        {
            'model_name': 'yolov11s',
            'detection_preset': 'high_sensitivity',
            'sahi_preset': 'high_detail',
            'description': 'High sensitivity detection'
        },
        # Current optimized config
        {
            'model_name': 'yolov11s',
            'detection_preset': 'shibuya_optimized',
            'sahi_preset': 'high_detail',
            'description': 'Current optimized config'
        },
        # Balanced approach
        {
            'model_name': 'yolov11s',
            'detection_preset': 'balanced',
            'sahi_preset': 'balanced',
            'description': 'Balanced approach'
        }
    ]
    
    results = []
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n🧪 Test {i}/{len(test_configs)}: {config['description']}")
        
        result = test_configuration(
            config['model_name'],
            config['detection_preset'],
            config['sahi_preset']
        )
        
        if result:
            results.append(result)
        
        # Wait between tests to avoid overwhelming the system
        if i < len(test_configs):
            print("⏳ Waiting 5 seconds before next test...")
            time.sleep(5)
    
    # Analyze results
    print("\n" + "=" * 60)
    print("📊 OPTIMIZATION RESULTS SUMMARY")
    print("=" * 60)
    
    if not results:
        print("❌ No successful tests completed")
        return
    
    # Sort by total detections (descending)
    results.sort(key=lambda x: x['total_detections'], reverse=True)
    
    print(f"\n🏆 BEST CONFIGURATIONS (by detection count):")
    print("-" * 60)
    
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['model_name']} + {result['detection_preset']} + {result['sahi_preset']}")
        print(f"   Detections: {result['total_detections']}")
        print(f"   Avg/frame: {result['avg_count_per_frame']:.2f}")
        print(f"   Speed: {result['fps']:.2f} FPS")
        print(f"   Time: {result['processing_time']:.1f}s")
        print()
    
    # Recommendations
    best_detection = results[0]
    fastest = min(results, key=lambda x: x['processing_time'])
    
    print("🎯 RECOMMENDATIONS:")
    print("-" * 30)
    print(f"🥇 Best Detection: {best_detection['model_name']} + {best_detection['detection_preset']} + {best_detection['sahi_preset']}")
    print(f"   → {best_detection['total_detections']} detections in {best_detection['processing_time']:.1f}s")
    
    print(f"⚡ Fastest: {fastest['model_name']} + {fastest['detection_preset']} + {fastest['sahi_preset']}")
    print(f"   → {fastest['total_detections']} detections in {fastest['processing_time']:.1f}s")
    
    # Set best configuration
    if best_detection['total_detections'] > 0:
        print(f"\n🔧 Setting best configuration as default...")
        try:
            requests.post(
                "http://localhost:8000/config/update",
                params={
                    "model_name": best_detection['model_name'],
                    "detection_preset": best_detection['detection_preset'],
                    "sahi_preset": best_detection['sahi_preset']
                },
                timeout=30
            )
            print("✅ Best configuration applied!")
        except Exception as e:
            print(f"❌ Failed to apply best configuration: {e}")

if __name__ == "__main__":
    run_optimization_tests()
