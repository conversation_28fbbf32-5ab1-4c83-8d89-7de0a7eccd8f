#!/usr/bin/env python3
"""
Test script for Shibuya crossing video analysis
"""

import requests
import json
import os
import time

def test_shibuya_video():
    """Test video analysis with Shibuya crossing video"""
    
    shibuya_path = r"C:\Users\<USER>\Downloads\Shibuya scramble crossing tokyo pedestrian travel japan shibuya shibuyascramble.mp4"
    
    if not os.path.exists(shibuya_path):
        print(f"❌ Shibuya video file not found: {shibuya_path}")
        print("Please make sure the video file exists at the specified path.")
        return False
    
    print(f"🗾 Testing Shibuya Crossing Video Analysis")
    print("=" * 60)
    print(f"Video file: {shibuya_path}")
    print(f"File size: {os.path.getsize(shibuya_path) / 1024 / 1024:.2f} MB")
    
    try:
        # Prepare the file for upload
        with open(shibuya_path, 'rb') as video_file:
            files = {
                'video_file': (os.path.basename(shibuya_path), video_file, 'video/mp4')
            }
            
            print("\n🚀 Uploading and analyzing Shibuya crossing video...")
            print("⚠️  This may take several minutes due to the video size and complexity...")
            print("📊 The system will use YOLO+SAHI for person detection and CSRNet for crowd density estimation")
            
            start_time = time.time()
            
            # Send request with longer timeout for large video
            response = requests.post(
                "http://localhost:8000/analyze-video", 
                files=files,
                timeout=600  # 10 minute timeout for large video
            )
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            results = response.json()
            print(f"\n✅ Analysis completed successfully!")
            print(f"⏱️  Processing time: {processing_time:.1f} seconds")
            print("=" * 60)
            print(f"🎯 SHIBUYA CROSSING ANALYSIS RESULTS")
            print("=" * 60)
            
            # Main results
            print(f"📈 Total estimated count: {results['total_estimated_count']}")
            print(f"📊 Average count per frame: {results['average_count_per_frame']:.2f}")
            print(f"🎬 Total frames analyzed: {results['total_frames_analyzed']}")
            print(f"🖼️  Annotated frames available: {len(results['annotated_frames'])}")
            
            # Model information
            if 'details' in results and 'model_versions' in results['details']:
                print(f"\n🤖 Model Information:")
                yolo_info = results['details']['model_versions']['yolo']
                crowd_info = results['details']['model_versions']['crowd_counting']
                
                if isinstance(yolo_info, dict):
                    print(f"  🎯 YOLO: {yolo_info.get('model_type', 'Unknown')} - {yolo_info.get('status', 'Unknown')}")
                    if yolo_info.get('sahi_enabled'):
                        print(f"      SAHI: Enabled (slice size: {yolo_info.get('sahi_slice_height', 'N/A')}x{yolo_info.get('sahi_slice_width', 'N/A')})")
                        print(f"      Method: {yolo_info.get('inference_method', 'N/A')}")
                else:
                    print(f"  🎯 YOLO: {yolo_info}")
                
                if isinstance(crowd_info, dict):
                    print(f"  🧠 Crowd Counter: {crowd_info.get('model_type', 'Unknown')}")
                    if crowd_info.get('csrnet_enabled'):
                        csrnet_status = crowd_info.get('csrnet_status', 'unknown')
                        print(f"      CSRNet: {csrnet_status}")
                        if 'csrnet_details' in crowd_info:
                            csrnet_details = crowd_info['csrnet_details']
                            print(f"      Method: {csrnet_details.get('description', 'N/A')}")
                else:
                    print(f"  🧠 Crowd Counter: {crowd_info}")
            
            # Summary statistics
            if 'details' in results and 'summary_statistics' in results['details']:
                stats = results['details']['summary_statistics']
                print(f"\n📈 Summary Statistics:")
                print(f"  Max count per frame: {stats.get('max_count_per_frame', 'N/A')}")
                print(f"  Min count per frame: {stats.get('min_count_per_frame', 'N/A')}")
                print(f"  Average density score: {stats.get('avg_density_score', 'N/A'):.3f}")
            
            # Sample frame analysis
            if 'details' in results and 'frames' in results['details']:
                frames = results['details']['frames']
                print(f"\n🎬 Sample Frame Analysis:")
                
                # Show first, middle, and last frames
                sample_indices = [0, len(frames)//2, len(frames)-1] if len(frames) > 2 else range(len(frames))
                
                for i in sample_indices:
                    if i < len(frames):
                        frame = frames[i]
                        print(f"  Frame {frame['frame_number']} (t={frame['timestamp']:.1f}s):")
                        print(f"    Detected: {frame['detections_count']} persons")
                        print(f"    Estimated: {frame.get('estimated_count', 'N/A')} persons")
                        print(f"    Density score: {frame['density_score']:.3f}")
                        print(f"    Method: {frame.get('method_used', 'N/A')}")
                        if frame['crowd_analysis']['distribution'] != 'empty':
                            print(f"    Distribution: {frame['crowd_analysis']['distribution']}")
            
            # Annotated frames
            if results['annotated_frames']:
                print(f"\n🖼️  Sample Annotated Frames:")
                for i, frame_url in enumerate(results['annotated_frames'][:5], 1):  # Show first 5
                    print(f"  {i}. http://localhost:8000{frame_url}")
                if len(results['annotated_frames']) > 5:
                    print(f"  ... and {len(results['annotated_frames']) - 5} more frames")
            
            print(f"\n🌐 Access Results:")
            print(f"  Web Interface: http://localhost:8000")
            print(f"  View all annotated frames in the static/ directory")
            
            # Performance metrics
            frames_per_second = results['total_frames_analyzed'] / processing_time
            print(f"\n⚡ Performance Metrics:")
            print(f"  Processing speed: {frames_per_second:.2f} FPS")
            print(f"  Average time per frame: {processing_time / results['total_frames_analyzed']:.2f}s")
            
            return True
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error details: {error_detail}")
            except:
                print(f"Error response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out. The video is very large and processing is taking too long.")
        print("💡 Try with a shorter video clip or increase the timeout.")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    print("🎥 AI Crowd POC - Shibuya Crossing Analysis")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly. Please check if it's running.")
            return
    except:
        print("❌ Cannot connect to server. Please make sure it's running on http://localhost:8000")
        print("Run: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
        return
    
    print("✅ Server is running")
    
    # Test Shibuya video
    success = test_shibuya_video()
    
    if success:
        print("\n🎉 Shibuya crossing analysis completed successfully!")
        print("🗾 This demonstrates the system's capability to handle real-world crowd scenarios.")
    else:
        print("\n❌ Analysis failed. Check the server logs for more details.")

if __name__ == "__main__":
    main()
