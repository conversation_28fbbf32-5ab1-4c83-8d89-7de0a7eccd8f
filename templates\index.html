<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Crowd POC - Person Counting</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 2px dashed #ddd;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
        }
        .frames-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .frame-item img {
            width: 100%;
            height: auto;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }

        /* Enhanced Configuration Styles */
        .preset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .preset-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            border: none;
        }

        .preset-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .preset-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .preset-title {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .preset-desc {
            font-size: 12px;
            opacity: 0.9;
        }

        .config-help {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
            font-style: italic;
        }

        .advanced-params {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            border-left: 4px solid #007bff;
        }

        .config-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .config-row label {
            min-width: 150px;
            font-weight: 500;
        }

        .config-row input[type="range"] {
            flex: 1;
        }

        .config-row span {
            min-width: 40px;
            text-align: center;
            font-weight: bold;
            color: #007bff;
        }

        .toggle-btn {
            background: linear-gradient(45deg, #FE6B8B 30%, #FF8E53 90%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .toggle-btn:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 AI Crowd POC - Person Counting</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            Upload a video or provide a URL to analyze crowd density and count people
        </p>

        <!-- Enhanced Configuration Panel -->
        <div class="config-panel" id="configPanel" style="background-color: #f8f9fa; padding: 25px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <h3 style="margin-top: 0; color: #495057; display: flex; align-items: center; gap: 10px;">
                ⚙️ Advanced Configuration
                <span style="font-size: 14px; background: #007bff; color: white; padding: 2px 8px; border-radius: 12px;">Enhanced</span>
            </h3>

            <!-- Scenario-based Quick Presets -->
            <div class="preset-section" style="margin-bottom: 25px;">
                <h4 style="color: #495057; margin-bottom: 15px;">🎯 Scenario-based Presets</h4>
                <div class="preset-grid">
                    <div class="preset-card" onclick="loadPreset('shibuya_crossing')">
                        <div class="preset-icon">🏙️</div>
                        <div class="preset-title">Shibuya Crossing</div>
                        <div class="preset-desc">Dense urban crossings</div>
                    </div>
                    <div class="preset-card" onclick="loadPreset('indoor_crowd')">
                        <div class="preset-icon">🏢</div>
                        <div class="preset-title">Indoor Crowd</div>
                        <div class="preset-desc">Events & gatherings</div>
                    </div>
                    <div class="preset-card" onclick="loadPreset('outdoor_event')">
                        <div class="preset-icon">🎪</div>
                        <div class="preset-title">Outdoor Event</div>
                        <div class="preset-desc">Concerts & festivals</div>
                    </div>
                    <div class="preset-card" onclick="loadPreset('security_monitoring')">
                        <div class="preset-icon">🔒</div>
                        <div class="preset-title">Security</div>
                        <div class="preset-desc">High accuracy monitoring</div>
                    </div>
                    <div class="preset-card" onclick="loadPreset('real_time')">
                        <div class="preset-icon">⚡</div>
                        <div class="preset-title">Real-time</div>
                        <div class="preset-desc">Fast processing</div>
                    </div>
                </div>
            </div>

            <!-- Manual Configuration -->
            <div class="manual-config">
                <h4 style="color: #495057; margin-bottom: 15px;">🔧 Manual Configuration</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                    <div>
                        <label for="modelSelect" style="font-size: 14px; font-weight: 600;">YOLO Model:</label>
                        <select id="modelSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <!-- Will be populated dynamically -->
                        </select>
                        <div class="config-help" id="modelHelp">Loading models...</div>
                    </div>
                    <div>
                        <label for="detectionSelect" style="font-size: 14px; font-weight: 600;">Detection Preset:</label>
                        <select id="detectionSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <!-- Will be populated dynamically -->
                        </select>
                        <div class="config-help" id="detectionHelp">Loading presets...</div>
                    </div>
                    <div>
                        <label for="sahiSelect" style="font-size: 14px; font-weight: 600;">SAHI Preset:</label>
                        <select id="sahiSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <!-- Will be populated dynamically -->
                        </select>
                        <div class="config-help" id="sahiHelp">Loading SAHI options...</div>
                    </div>
                </div>

                <!-- Advanced Parameters -->
                <div class="advanced-params" id="advancedParams" style="display: none;">
                    <h5 style="margin-top: 0; color: #495057;">⚡ Advanced Parameters</h5>
                    <div class="config-row">
                        <label for="confidenceThreshold">Confidence Threshold:</label>
                        <input type="range" id="confidenceThreshold" min="0.1" max="0.9" step="0.05" value="0.4">
                        <span id="confidenceValue">0.4</span>
                    </div>
                    <div class="config-row">
                        <label for="nmsThreshold">NMS Threshold:</label>
                        <input type="range" id="nmsThreshold" min="0.1" max="0.7" step="0.05" value="0.4">
                        <span id="nmsValue">0.4</span>
                    </div>
                    <div class="config-row">
                        <label for="maxFrames">Max Frames:</label>
                        <input type="number" id="maxFrames" min="10" max="500" value="50" style="width: 80px; padding: 4px;">
                        <span style="font-size: 12px; color: #666;">frames</span>
                    </div>
                    <div class="config-row">
                        <label for="frameInterval">Frame Interval:</label>
                        <input type="number" id="frameInterval" min="0.5" max="5.0" step="0.5" value="1.0" style="width: 80px; padding: 4px;">
                        <span style="font-size: 12px; color: #666;">seconds</span>
                    </div>
                </div>

                <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 15px;">
                    <button type="button" onclick="updateConfiguration()" style="background-color: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: 500;">🔄 Update Config</button>
                    <button type="button" onclick="getCurrentConfig()" style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: 500;">📋 Show Current</button>
                    <button type="button" onclick="toggleAdvanced()" class="toggle-btn">⚙️ Advanced</button>
                    <button type="button" onclick="resetToDefaults()" style="background-color: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-weight: 500;">🔄 Reset</button>
                </div>
            </div>

            <div id="configStatus" style="margin-top: 15px; padding: 12px; border-radius: 6px; display: none; font-weight: 500;"></div>
        </div>
        
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-section">
                <div class="form-group">
                    <label for="videoFile">Upload Video File:</label>
                    <input type="file" id="videoFile" name="video_file" accept="video/*">
                </div>
                
                <div style="margin: 20px 0; color: #666;">OR</div>
                
                <div class="form-group">
                    <label for="videoUrl">Video URL:</label>
                    <input type="url" id="videoUrl" name="video_url" placeholder="https://example.com/video.mp4">
                </div>
            </div>
            
            <button type="submit" id="analyzeBtn">🔍 Analyze Video</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Processing video... This may take a few minutes.</p>
        </div>
        
        <div class="results" id="results">
            <h2>📊 Analysis Results</h2>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const videoFile = document.getElementById('videoFile').files[0];
            const videoUrl = document.getElementById('videoUrl').value;
            
            if (!videoFile && !videoUrl) {
                alert('Please provide either a video file or a video URL');
                return;
            }
            
            if (videoFile && videoUrl) {
                alert('Please provide either a video file or a video URL, not both');
                return;
            }
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('analyzeBtn').disabled = true;
            
            try {
                const formData = new FormData();
                if (videoFile) {
                    formData.append('video_file', videoFile);
                } else {
                    formData.append('video_url', videoUrl);
                }
                
                const response = await fetch('/analyze-video', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const results = await response.json();
                displayResults(results);
                
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('resultsContent').innerHTML = 
                    `<div class="error">Error: ${error.message}</div>`;
                document.getElementById('results').style.display = 'block';
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('analyzeBtn').disabled = false;
            }
        });
        
        function displayResults(data) {
            const resultsContent = document.getElementById('resultsContent');

            // Check if this is tracking-based analysis
            const isTrackingBased = data.analysis_type === 'tracking_based';

            let html = `
                <div class="result-item">
                    <h3>📈 Analysis Summary</h3>
                    ${isTrackingBased ? `
                        <p><strong>🎯 Unique Persons Detected:</strong> ${data.video_summary?.unique_persons_detected || 'N/A'}</p>
                        <p><strong>👥 Max Simultaneous Persons:</strong> ${data.video_summary?.max_simultaneous_persons || 'N/A'}</p>
                        <p><strong>📊 Average Persons per Frame:</strong> ${data.video_summary?.average_persons_per_frame?.toFixed(2) || 'N/A'}</p>
                        <p><strong>🔍 Analysis Method:</strong> <span style="color: #4CAF50; font-weight: bold;">Tracking-based (Unique Counting)</span></p>
                        <p><strong>📈 Detection Reliability:</strong> ${(data.video_summary?.detection_reliability * 100)?.toFixed(1) || 'N/A'}%</p>
                    ` : `
                        <p><strong>Total Estimated Count:</strong> ${data.total_estimated_count}</p>
                        <p><strong>Average Count per Frame:</strong> ${data.average_count_per_frame?.toFixed(2) || 'N/A'}</p>
                        <p><strong>🔍 Analysis Method:</strong> <span style="color: #FF9800;">Frame-based (Traditional)</span></p>
                    `}
                    <p><strong>Total Frames Analyzed:</strong> ${data.total_frames_analyzed || data.frame_results?.length || 'N/A'}</p>
                </div>
            `;
            
            // Display frames (works for both traditional and tracking-based)
            const frameUrls = data.annotated_frames || data.annotated_frame_urls || [];
            if (frameUrls.length > 0) {
                html += `
                    <div class="result-item">
                        <h3>🖼️ ${isTrackingBased ? 'Tracked Frames' : 'Sample Frames'}</h3>
                        <div class="frames-grid">
                `;

                frameUrls.forEach((frameUrl, index) => {
                    const frameResult = isTrackingBased ? data.frame_results?.[index] : null;
                    html += `
                        <div class="frame-item">
                            <img src="${frameUrl}" alt="Frame ${index + 1}">
                            <p style="text-align: center; margin-top: 5px;">Frame ${index + 1}</p>
                            ${frameResult ? `
                                <small style="color: #666; text-align: center; display: block;">
                                    Tracked: ${frameResult.tracked_persons} |
                                    Method: ${frameResult.counting_method}
                                </small>
                            ` : ''}
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }
            
            // Technical details
            if (data.details || isTrackingBased) {
                html += `
                    <div class="result-item">
                        <h3>🔧 Technical Details</h3>
                        ${data.details ? `
                            <p><strong>Processing Time:</strong> ${data.details.processing_timestamp}</p>
                            <p><strong>YOLO Model:</strong> ${data.details.model_versions?.yolo || 'N/A'}</p>
                            <p><strong>Crowd Counting Model:</strong> ${data.details.model_versions?.crowd_counting || 'N/A'}</p>
                        ` : ''}
                        ${isTrackingBased && data.video_summary?.tracking_statistics ? `
                            <p><strong>Tracking Algorithm:</strong> SORT (Simple Online Real-time Tracking)</p>
                            <p><strong>Average Track Duration:</strong> ${data.video_summary.tracking_statistics.average_track_duration?.toFixed(1) || 'N/A'} frames</p>
                            <p><strong>Tracking Quality:</strong> ${(data.video_summary.tracking_quality * 100)?.toFixed(1) || 'N/A'}%</p>
                        ` : ''}
                    </div>
                `;
            }
            
            resultsContent.innerHTML = html;
            document.getElementById('results').style.display = 'block';
        }
        
        // Clear URL when file is selected
        document.getElementById('videoFile').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('videoUrl').value = '';
            }
        });
        
        // Clear file when URL is entered
        document.getElementById('videoUrl').addEventListener('input', function() {
            if (this.value) {
                document.getElementById('videoFile').value = '';
            }
        });

        // Configuration functions
        async function updateConfiguration() {
            const model = document.getElementById('modelSelect').value;
            const detection = document.getElementById('detectionSelect').value;
            const sahi = document.getElementById('sahiSelect').value;

            // Get advanced parameters
            const confidence = document.getElementById('confidenceThreshold').value;
            const nms = document.getElementById('nmsThreshold').value;
            const maxDet = document.getElementById('maxDetections').value;
            const sliceHeight = document.getElementById('sliceHeight').value;
            const sliceWidth = document.getElementById('sliceWidth').value;
            const overlapHeight = document.getElementById('overlapHeight').value;
            const overlapWidth = document.getElementById('overlapWidth').value;

            const statusDiv = document.getElementById('configStatus');
            statusDiv.style.display = 'block';
            statusDiv.style.backgroundColor = '#fff3cd';
            statusDiv.style.color = '#856404';
            statusDiv.innerHTML = '⏳ Updating configuration...';

            try {
                // Build query parameters
                const params = new URLSearchParams();
                params.append('model_name', model);
                params.append('detection_preset', detection);
                params.append('sahi_preset', sahi);
                params.append('confidence_threshold', confidence);
                params.append('nms_threshold', nms);
                params.append('max_detections', maxDet);
                params.append('slice_height', sliceHeight);
                params.append('slice_width', sliceWidth);
                params.append('overlap_height_ratio', overlapHeight);
                params.append('overlap_width_ratio', overlapWidth);

                const response = await fetch(`/config/update?${params.toString()}`, {
                    method: 'POST'
                });

                const result = await response.json();

                if (response.ok) {
                    statusDiv.style.backgroundColor = '#d4edda';
                    statusDiv.style.color = '#155724';
                    statusDiv.innerHTML = `✅ Configuration updated successfully!<br>
                        <small>Model: ${result.current_config.model_path}<br>
                        Confidence: ${result.current_config.confidence_threshold}<br>
                        SAHI: ${result.current_config.slice_size}</small>`;
                } else {
                    throw new Error(result.detail || 'Update failed');
                }
            } catch (error) {
                statusDiv.style.backgroundColor = '#f8d7da';
                statusDiv.style.color = '#721c24';
                statusDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }

        async function loadPreset(scenario) {
            const statusDiv = document.getElementById('configStatus');
            statusDiv.style.display = 'block';
            statusDiv.style.backgroundColor = '#fff3cd';
            statusDiv.style.color = '#856404';
            statusDiv.innerHTML = `⏳ Loading ${scenario} preset...`;

            try {
                const response = await fetch('/config/scenarios');
                const scenarios = await response.json();

                if (scenarios[scenario]) {
                    const config = scenarios[scenario];
                    document.getElementById('modelSelect').value = config.yolo_model;
                    document.getElementById('detectionSelect').value = config.detection_preset;
                    document.getElementById('sahiSelect').value = config.sahi_preset;

                    await updateConfiguration();
                } else {
                    throw new Error(`Scenario ${scenario} not found`);
                }
            } catch (error) {
                statusDiv.style.backgroundColor = '#f8d7da';
                statusDiv.style.color = '#721c24';
                statusDiv.innerHTML = `❌ Error loading preset: ${error.message}`;
            }
        }

        async function getCurrentConfig() {
            const statusDiv = document.getElementById('configStatus');
            statusDiv.style.display = 'block';
            statusDiv.style.backgroundColor = '#d1ecf1';
            statusDiv.style.color = '#0c5460';
            statusDiv.innerHTML = '⏳ Loading current configuration...';

            try {
                const response = await fetch('/config/current');
                const result = await response.json();

                if (result.status === 'initialized') {
                    const config = result.config;
                    statusDiv.innerHTML = `📋 Current Configuration:<br>
                        <small><strong>Model:</strong> ${config.model_path}<br>
                        <strong>Confidence:</strong> ${config.confidence_threshold}<br>
                        <strong>NMS:</strong> ${config.nms_threshold}<br>
                        <strong>SAHI:</strong> ${config.use_sahi ? 'Enabled' : 'Disabled'}<br>
                        <strong>Slice Size:</strong> ${config.slice_size}<br>
                        <strong>Overlap:</strong> ${config.overlap_ratio}</small>`;
                } else {
                    statusDiv.innerHTML = '⚠️ Models not initialized';
                }
            } catch (error) {
                statusDiv.style.backgroundColor = '#f8d7da';
                statusDiv.style.color = '#721c24';
                statusDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }

        // Enhanced configuration functions
        async function loadAvailableOptions() {
            try {
                const response = await fetch('/config/models');
                const data = await response.json();

                // Populate YOLO models
                const modelSelect = document.getElementById('modelSelect');
                modelSelect.innerHTML = '';
                Object.entries(data.yolo_models).forEach(([key, description]) => {
                    const option = document.createElement('option');
                    option.value = key;
                    option.textContent = `${key.toUpperCase()} - ${description}`;
                    if (key === 'yolov11s') option.selected = true;
                    modelSelect.appendChild(option);
                });

                // Populate detection presets
                const detectionSelect = document.getElementById('detectionSelect');
                detectionSelect.innerHTML = '';
                Object.entries(data.detection_presets).forEach(([key, description]) => {
                    const option = document.createElement('option');
                    option.value = key;
                    option.textContent = `${key.replace('_', ' ').toUpperCase()} - ${description}`;
                    if (key === 'balanced') option.selected = true;
                    detectionSelect.appendChild(option);
                });

                // Populate SAHI presets
                const sahiSelect = document.getElementById('sahiSelect');
                sahiSelect.innerHTML = '';
                Object.entries(data.sahi_presets).forEach(([key, description]) => {
                    const option = document.createElement('option');
                    option.value = key;
                    option.textContent = `${key.replace('_', ' ').toUpperCase()} - ${description}`;
                    if (key === 'balanced') option.selected = true;
                    sahiSelect.appendChild(option);
                });

                // Update help texts
                updateHelpTexts();

            } catch (error) {
                console.error('Failed to load available options:', error);
                document.getElementById('modelHelp').textContent = 'Failed to load models';
                document.getElementById('detectionHelp').textContent = 'Failed to load presets';
                document.getElementById('sahiHelp').textContent = 'Failed to load SAHI options';
            }
        }

        function updateHelpTexts() {
            // Update help texts based on current selections
            const modelSelect = document.getElementById('modelSelect');
            const detectionSelect = document.getElementById('detectionSelect');
            const sahiSelect = document.getElementById('sahiSelect');

            document.getElementById('modelHelp').textContent =
                modelSelect.options[modelSelect.selectedIndex]?.text.split(' - ')[1] || '';
            document.getElementById('detectionHelp').textContent =
                detectionSelect.options[detectionSelect.selectedIndex]?.text.split(' - ')[1] || '';
            document.getElementById('sahiHelp').textContent =
                sahiSelect.options[sahiSelect.selectedIndex]?.text.split(' - ')[1] || '';
        }

        function toggleAdvanced() {
            const advancedParams = document.getElementById('advancedParams');
            const isVisible = advancedParams.style.display !== 'none';
            advancedParams.style.display = isVisible ? 'none' : 'block';
        }

        async function resetToDefaults() {
            const statusDiv = document.getElementById('configStatus');
            statusDiv.style.display = 'block';
            statusDiv.style.backgroundColor = '#fff3cd';
            statusDiv.style.color = '#856404';
            statusDiv.innerHTML = '⏳ Resetting to defaults...';

            // Reset to default values
            document.getElementById('modelSelect').value = 'yolov11s';
            document.getElementById('detectionSelect').value = 'balanced';
            document.getElementById('sahiSelect').value = 'balanced';
            document.getElementById('confidenceThreshold').value = 0.4;
            document.getElementById('nmsThreshold').value = 0.4;
            document.getElementById('maxFrames').value = 50;
            document.getElementById('frameInterval').value = 1.0;

            // Update displays
            document.getElementById('confidenceValue').textContent = '0.4';
            document.getElementById('nmsValue').textContent = '0.4';

            await updateConfiguration();
        }

        // Add event listeners for range inputs
        document.addEventListener('DOMContentLoaded', function() {
            const confidenceSlider = document.getElementById('confidenceThreshold');
            const nmsSlider = document.getElementById('nmsThreshold');

            if (confidenceSlider) {
                confidenceSlider.addEventListener('input', function() {
                    document.getElementById('confidenceValue').textContent = this.value;
                });
            }

            if (nmsSlider) {
                nmsSlider.addEventListener('input', function() {
                    document.getElementById('nmsValue').textContent = this.value;
                });
            }

            // Add change listeners for help text updates
            ['modelSelect', 'detectionSelect', 'sahiSelect'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', updateHelpTexts);
                }
            });
        });

        // Load available options and current config on page load
        window.addEventListener('load', async function() {
            await loadAvailableOptions();
            await getCurrentConfig();
        });
    </script>
</body>
</html>
