#!/usr/bin/env python3
"""
CSRNet implementation for crowd counting
Based on "CSRNet: Dilated Convolutional Neural Networks for Understanding the Highly Congested Scenes"
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
import numpy as np
import cv2
from typing import Tuple, Dict
import logging
import os
import requests
from PIL import Image
import torchvision.transforms as transforms

logger = logging.getLogger(__name__)

class CSRNet(nn.Module):
    """CSRNet model for crowd counting"""
    
    def __init__(self, load_weights=False):
        super(CSRNet, self).__init__()
        
        # Frontend: VGG16 backbone (first 10 layers)
        self.frontend_feat = [64, 64, 'M', 128, 128, 'M', 256, 256, 256, 'M', 512, 512, 512]
        self.frontend = make_layers(self.frontend_feat)
        
        # Backend: Dilated convolutions
        self.backend_feat = [512, 512, 512, 256, 128, 64]
        self.backend = make_layers(self.backend_feat, in_channels=512, dilation=True)
        
        # Output layer
        self.output_layer = nn.Conv2d(64, 1, kernel_size=1)
        
        if not load_weights:
            self._initialize_weights()
    
    def forward(self, x):
        x = self.frontend(x)
        x = self.backend(x)
        x = self.output_layer(x)
        return x
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.normal_(m.weight, std=0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

def make_layers(cfg, in_channels=3, batch_norm=False, dilation=False):
    """Create VGG-style layers"""
    if dilation:
        d_rate = 2
    else:
        d_rate = 1
    
    layers = []
    for v in cfg:
        if v == 'M':
            layers += [nn.MaxPool2d(kernel_size=2, stride=2)]
        else:
            conv2d = nn.Conv2d(in_channels, v, kernel_size=3, padding=d_rate, dilation=d_rate)
            if batch_norm:
                layers += [conv2d, nn.BatchNorm2d(v), nn.ReLU(inplace=True)]
            else:
                layers += [conv2d, nn.ReLU(inplace=True)]
            in_channels = v
    return nn.Sequential(*layers)

class CSRNetCrowdCounter:
    """CSRNet-based crowd counting system"""
    
    def __init__(self, model_path: str = None):
        """
        Initialize CSRNet crowd counter
        
        Args:
            model_path: Path to pretrained model weights
        """
        self.model_path = model_path
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # Model info
        self.model_info = {
            "model_type": "CSRNet",
            "version": "PyTorch Implementation",
            "description": "Dilated CNN for crowd counting",
            "input_size": "Variable (maintains aspect ratio)",
            "output": "Density map"
        }
    
    def load_model(self):
        """Load CSRNet model"""
        try:
            logger.info("Loading CSRNet model...")
            
            # Initialize model
            self.model = CSRNet()
            
            # Try to load pretrained weights if available
            if self.model_path and os.path.exists(self.model_path):
                logger.info(f"Loading pretrained weights from {self.model_path}")
                checkpoint = torch.load(self.model_path, map_location=self.device)
                self.model.load_state_dict(checkpoint)
            else:
                logger.warning("No pretrained weights found. Using randomly initialized model.")
                logger.info("For better results, download pretrained CSRNet weights.")
            
            self.model.to(self.device)
            self.model.eval()
            
            logger.info("CSRNet model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load CSRNet model: {e}")
            # Fallback to simple density estimation
            self.model = None
            logger.info("Using fallback density estimation")
    
    def estimate_crowd_count(self, image: np.ndarray, detections: list = None) -> Dict:
        """
        Estimate crowd count using CSRNet
        
        Args:
            image: Input image as numpy array (BGR)
            detections: Optional YOLO detections for hybrid approach
            
        Returns:
            Dictionary with count estimation and density map
        """
        try:
            if self.model is None:
                # Fallback to detection-based counting
                return self._fallback_counting(image, detections)
            
            # Convert BGR to RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Prepare image for model
            pil_image = Image.fromarray(image_rgb)
            
            # Get original size
            original_size = pil_image.size
            
            # Resize to multiple of 8 (CSRNet requirement)
            new_width = (original_size[0] // 8) * 8
            new_height = (original_size[1] // 8) * 8
            
            if new_width == 0 or new_height == 0:
                new_width, new_height = 512, 384
            
            resized_image = pil_image.resize((new_width, new_height), Image.LANCZOS)
            
            # Transform image
            input_tensor = self.transform(resized_image).unsqueeze(0).to(self.device)
            
            # Forward pass
            with torch.no_grad():
                density_map = self.model(input_tensor)
                density_map = density_map.squeeze().cpu().numpy()
            
            # Calculate total count
            total_count = np.sum(density_map)
            
            # Resize density map back to original size
            density_map_resized = cv2.resize(
                density_map, 
                (image.shape[1], image.shape[0]), 
                interpolation=cv2.INTER_LINEAR
            )
            
            # Create visualization heatmap
            heatmap = self._create_heatmap(density_map_resized)
            
            result = {
                'estimated_count': max(0, int(total_count)),
                'density_map': density_map_resized,
                'heatmap': heatmap,
                'method': 'CSRNet',
                'confidence': 0.8 if total_count > 0 else 0.5
            }
            
            # If we have YOLO detections, combine the results
            if detections:
                yolo_count = len(detections)
                # Weighted average (favor CSRNet for dense crowds, YOLO for sparse)
                if total_count > yolo_count * 1.5:
                    # Dense crowd - trust CSRNet more
                    combined_count = int(0.7 * total_count + 0.3 * yolo_count)
                    result['method'] = 'CSRNet+YOLO (CSRNet weighted)'
                else:
                    # Sparse crowd - trust YOLO more
                    combined_count = int(0.3 * total_count + 0.7 * yolo_count)
                    result['method'] = 'CSRNet+YOLO (YOLO weighted)'
                
                result['estimated_count'] = max(0, combined_count)
                result['yolo_count'] = yolo_count
                result['csrnet_count'] = int(total_count)
                result['confidence'] = 0.9
            
            return result
            
        except Exception as e:
            logger.error(f"Error in CSRNet crowd counting: {e}")
            return self._fallback_counting(image, detections)
    
    def _fallback_counting(self, image: np.ndarray, detections: list = None) -> Dict:
        """Fallback counting method using detections"""
        if detections:
            count = len(detections)
            # Create simple heatmap from detections
            heatmap = np.zeros((image.shape[0], image.shape[1]), dtype=np.float32)
            
            for det in detections:
                center = det.get('center', [0, 0])
                if 0 <= center[0] < image.shape[1] and 0 <= center[1] < image.shape[0]:
                    cv2.circle(heatmap, tuple(center), 20, 1.0, -1)
            
            # Apply Gaussian blur
            heatmap = cv2.GaussianBlur(heatmap, (41, 41), 15)
            
            return {
                'estimated_count': count,
                'density_map': heatmap,
                'heatmap': self._create_heatmap(heatmap),
                'method': 'YOLO Detection Count',
                'confidence': 0.7
            }
        else:
            return {
                'estimated_count': 0,
                'density_map': np.zeros((image.shape[0], image.shape[1]), dtype=np.float32),
                'heatmap': np.zeros((image.shape[0], image.shape[1], 3), dtype=np.uint8),
                'method': 'No Detection',
                'confidence': 0.1
            }
    
    def _create_heatmap(self, density_map: np.ndarray) -> np.ndarray:
        """Create colored heatmap from density map"""
        # Normalize density map
        if density_map.max() > 0:
            normalized = (density_map / density_map.max() * 255).astype(np.uint8)
        else:
            normalized = density_map.astype(np.uint8)
        
        # Apply colormap
        heatmap = cv2.applyColorMap(normalized, cv2.COLORMAP_JET)
        
        return heatmap
    
    def get_model_info(self) -> Dict:
        """Get model information"""
        info = self.model_info.copy()
        info['status'] = 'loaded' if self.model is not None else 'fallback'
        info['device'] = str(self.device)
        return info
