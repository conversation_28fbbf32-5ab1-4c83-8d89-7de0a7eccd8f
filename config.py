"""
Configuration settings for AI Crowd POC
Allows easy tuning of model parameters and detection settings
"""

import os
from typing import Dict, Any

class ModelConfig:
    """Configuration for AI models and detection parameters"""
    
    # YOLO Model Settings
    YOLO_MODELS = {
        'yolov8n': {
            'path': 'yolov8n.pt',
            'description': 'Nano - Fastest, lowest accuracy',
            'recommended_for': 'Real-time applications, simple scenes'
        },
        'yolov8s': {
            'path': 'yolov8s.pt', 
            'description': 'Small - Good balance of speed and accuracy',
            'recommended_for': 'General purpose crowd counting'
        },
        'yolov8m': {
            'path': 'yolov8m.pt',
            'description': 'Medium - Better accuracy, slower',
            'recommended_for': 'Complex scenes, better detection'
        },
        'yolov8l': {
            'path': 'yolov8l.pt',
            'description': 'Large - High accuracy, slower',
            'recommended_for': 'High accuracy requirements'
        },
        'yolov8x': {
            'path': 'yolov8x.pt',
            'description': 'Extra Large - Highest accuracy, slowest',
            'recommended_for': 'Maximum accuracy, offline processing'
        },
        'yolov11n': {
            'path': 'yolo11n.pt',
            'description': 'YOLOv11 Nano - Latest version, improved accuracy',
            'recommended_for': 'Latest improvements with speed'
        },
        'yolov11s': {
            'path': 'yolo11s.pt',
            'description': 'YOLOv11 Small - Latest version, balanced',
            'recommended_for': 'Best overall choice for crowd counting'
        },
        'yolov11m': {
            'path': 'yolo11m.pt',
            'description': 'YOLOv11 Medium - Latest version, high accuracy',
            'recommended_for': 'Complex crowd scenes like Shibuya'
        }
    }
    
    # Detection Parameters for Different Scenarios
    DETECTION_PRESETS = {
        'high_sensitivity': {
            'confidence_threshold': 0.25,
            'nms_threshold': 0.45,
            'description': 'Detect more objects, may include false positives',
            'recommended_for': 'Dense crowds, small objects, challenging videos'
        },
        'balanced': {
            'confidence_threshold': 0.4,
            'nms_threshold': 0.4,
            'description': 'Good balance between precision and recall',
            'recommended_for': 'General purpose crowd counting'
        },
        'high_precision': {
            'confidence_threshold': 0.6,
            'nms_threshold': 0.35,
            'description': 'Fewer false positives, may miss some objects',
            'recommended_for': 'Clean scenes, high accuracy requirements'
        },
        'shibuya_optimized': {
            'confidence_threshold': 0.2,
            'nms_threshold': 0.5,
            'description': 'Optimized for dense urban crossings',
            'recommended_for': 'Shibuya-like dense crowd scenarios'
        }
    }
    
    # SAHI Parameters for Different Scenarios
    SAHI_PRESETS = {
        'fast': {
            'slice_height': 640,
            'slice_width': 640,
            'overlap_height_ratio': 0.1,
            'overlap_width_ratio': 0.1,
            'description': 'Faster processing, may miss small objects'
        },
        'balanced': {
            'slice_height': 512,
            'slice_width': 512,
            'overlap_height_ratio': 0.2,
            'overlap_width_ratio': 0.2,
            'description': 'Good balance of speed and detection'
        },
        'high_detail': {
            'slice_height': 416,
            'slice_width': 416,
            'overlap_height_ratio': 0.3,
            'overlap_width_ratio': 0.3,
            'description': 'Better small object detection, slower'
        },
        'maximum_detection': {
            'slice_height': 320,
            'slice_width': 320,
            'overlap_height_ratio': 0.4,
            'overlap_width_ratio': 0.4,
            'description': 'Maximum small object detection, slowest'
        }
    }
    
    # Default Configuration
    DEFAULT_CONFIG = {
        'yolo_model': 'yolov11s',  # Changed from yolov8n to yolov11s
        'detection_preset': 'shibuya_optimized',  # Changed for better detection
        'sahi_preset': 'high_detail',  # Changed for better small object detection
        'use_sahi': True,
        'use_csrnet': True,
        'max_frames': 50,  # Limit for testing
        'frame_interval': 1.0,  # seconds
        'resize_max_width': 1920,
        'resize_max_height': 1080
    }
    
    @classmethod
    def get_yolo_config(cls, model_name: str = None, preset: str = None) -> Dict[str, Any]:
        """Get YOLO configuration for specified model and preset"""
        model_name = model_name or cls.DEFAULT_CONFIG['yolo_model']
        preset = preset or cls.DEFAULT_CONFIG['detection_preset']
        
        if model_name not in cls.YOLO_MODELS:
            raise ValueError(f"Unknown YOLO model: {model_name}")
        if preset not in cls.DETECTION_PRESETS:
            raise ValueError(f"Unknown detection preset: {preset}")
            
        return {
            'model_path': cls.YOLO_MODELS[model_name]['path'],
            'model_info': cls.YOLO_MODELS[model_name],
            **cls.DETECTION_PRESETS[preset]
        }
    
    @classmethod
    def get_sahi_config(cls, preset: str = None) -> Dict[str, Any]:
        """Get SAHI configuration for specified preset"""
        preset = preset or cls.DEFAULT_CONFIG['sahi_preset']
        
        if preset not in cls.SAHI_PRESETS:
            raise ValueError(f"Unknown SAHI preset: {preset}")
            
        return cls.SAHI_PRESETS[preset]
    
    @classmethod
    def get_recommended_config_for_scenario(cls, scenario: str) -> Dict[str, Any]:
        """Get recommended configuration for specific scenarios"""
        scenarios = {
            'shibuya_crossing': {
                'yolo_model': 'yolov11m',
                'detection_preset': 'shibuya_optimized',
                'sahi_preset': 'high_detail',
                'description': 'Optimized for dense urban crossings like Shibuya'
            },
            'indoor_crowd': {
                'yolo_model': 'yolov11s',
                'detection_preset': 'balanced',
                'sahi_preset': 'balanced',
                'description': 'Good for indoor events and gatherings'
            },
            'outdoor_event': {
                'yolo_model': 'yolov11s',
                'detection_preset': 'high_sensitivity',
                'sahi_preset': 'balanced',
                'description': 'Outdoor concerts, festivals, etc.'
            },
            'security_monitoring': {
                'yolo_model': 'yolov11l',
                'detection_preset': 'high_precision',
                'sahi_preset': 'high_detail',
                'description': 'High accuracy for security applications'
            },
            'real_time': {
                'yolo_model': 'yolov11n',
                'detection_preset': 'balanced',
                'sahi_preset': 'fast',
                'description': 'Real-time processing with acceptable accuracy'
            }
        }
        
        if scenario not in scenarios:
            raise ValueError(f"Unknown scenario: {scenario}")
            
        return scenarios[scenario]
    
    @classmethod
    def list_available_models(cls) -> Dict[str, str]:
        """List all available YOLO models with descriptions"""
        return {name: info['description'] for name, info in cls.YOLO_MODELS.items()}
    
    @classmethod
    def list_detection_presets(cls) -> Dict[str, str]:
        """List all detection presets with descriptions"""
        return {name: info['description'] for name, info in cls.DETECTION_PRESETS.items()}
    
    @classmethod
    def list_sahi_presets(cls) -> Dict[str, str]:
        """List all SAHI presets with descriptions"""
        return {name: info['description'] for name, info in cls.SAHI_PRESETS.items()}

# Environment-based configuration
class EnvironmentConfig:
    """Environment-specific configuration"""
    
    # Model download URLs for automatic downloading
    MODEL_URLS = {
        'yolov11n': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt',
        'yolov11s': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s.pt',
        'yolov11m': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m.pt',
        'yolov11l': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11l.pt',
        'yolov11x': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11x.pt'
    }
    
    # CSRNet pretrained weights (if available)
    CSRNET_WEIGHTS_URL = "https://drive.google.com/uc?id=1nnx5p7t1kcaVtcOptHjxgHtJJy6sccvW"  # Example URL
    
    @classmethod
    def get_model_path(cls, model_name: str) -> str:
        """Get local path for model, download if necessary"""
        model_filename = ModelConfig.YOLO_MODELS[model_name]['path']
        
        if not os.path.exists(model_filename):
            print(f"Model {model_filename} not found. It will be downloaded automatically by ultralytics.")
        
        return model_filename
