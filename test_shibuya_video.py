#!/usr/bin/env python3
"""
Test script for Shibuya crossing video analysis
"""

import requests
import json
import os
import time

def test_shibuya_video():
    """Test video analysis with Shibuya crossing video"""
    
    shibuya_path = r"C:\Users\<USER>\Downloads\Shibuya scramble crossing tokyo pedestrian travel japan shibuya shibuyascramble.mp4"
    
    if not os.path.exists(shibuya_path):
        print(f"❌ Shibuya video file not found: {shibuya_path}")
        print("Please make sure the video file exists at the specified path.")
        return False
    
    print(f"🗾 Testing Shibuya Crossing Video Analysis")
    print("=" * 60)
    print(f"Video file: {shibuya_path}")
    print(f"File size: {os.path.getsize(shibuya_path) / 1024 / 1024:.2f} MB")
    
    try:
        # Prepare the file for upload
        with open(shibuya_path, 'rb') as video_file:
            files = {
                'video_file': (os.path.basename(shibuya_path), video_file, 'video/mp4')
            }
            
            print("\n🚀 Uploading and analyzing Shibuya crossing video...")
            print("⚠️  This may take several minutes due to the video size and complexity...")
            print("🎯 The system will use HYBRID TRACKING-BASED COUNTING:")
            print("   • YOLO+SAHI for person detection")
            print("   • SORT tracking for unique person identification")
            print("   • CSRNet for crowd density estimation")
            print("   • Hybrid approach based on crowd density")
            
            start_time = time.time()
            
            # Send request with longer timeout for large video
            response = requests.post(
                "http://localhost:8000/analyze-video", 
                files=files,
                timeout=600  # 10 minute timeout for large video
            )
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            results = response.json()
            print(f"\n✅ Analysis completed successfully!")
            print(f"⏱️  Processing time: {processing_time:.1f} seconds")
            print("=" * 60)
            print(f"🎯 SHIBUYA CROSSING ANALYSIS RESULTS")
            print("=" * 60)

            # Check if this is tracking-based analysis
            is_tracking_based = results.get('analysis_type') == 'tracking_based'

            if is_tracking_based and 'video_summary' in results:
                # New tracking-based results
                summary = results['video_summary']
                print(f"🎯 UNIQUE PERSONS DETECTED: {summary.get('unique_persons_detected', 'N/A')}")
                print(f"👥 Max simultaneous persons: {summary.get('max_simultaneous_persons', 'N/A')}")
                print(f"📊 Average persons per frame: {summary.get('average_persons_per_frame', 'N/A'):.2f}")
                print(f"🎬 Total frames analyzed: {summary.get('total_frames_processed', 'N/A')}")
                print(f"🔍 Analysis method: HYBRID TRACKING-BASED")
                print(f"📈 Detection reliability: {(summary.get('detection_reliability', 0) * 100):.1f}%")
                print(f"🎯 Tracking quality: {(summary.get('tracking_quality', 0) * 100):.1f}%")

                # Tracking statistics
                if 'tracking_statistics' in summary:
                    track_stats = summary['tracking_statistics']
                    print(f"📊 Average track duration: {track_stats.get('average_track_duration', 'N/A'):.1f} frames")
                    print(f"🔄 Total person detections: {track_stats.get('total_detections', 'N/A')}")

                frame_urls = results.get('annotated_frame_urls', [])
                print(f"�️  Tracked frames available: {len(frame_urls)}")
            else:
                # Traditional results (fallback)
                print(f"�📈 Total estimated count: {results.get('total_estimated_count', 'N/A')}")
                print(f"📊 Average count per frame: {results.get('average_count_per_frame', 0):.2f}")
                print(f"🎬 Total frames analyzed: {results.get('total_frames_analyzed', 'N/A')}")
                frame_urls = results.get('annotated_frames', [])
                print(f"🖼️  Annotated frames available: {len(frame_urls)}")
            
            # Model information
            if 'details' in results and 'model_versions' in results['details']:
                print(f"\n🤖 Model Information:")
                yolo_info = results['details']['model_versions']['yolo']
                crowd_info = results['details']['model_versions']['crowd_counting']
                
                if isinstance(yolo_info, dict):
                    print(f"  🎯 YOLO: {yolo_info.get('model_type', 'Unknown')} - {yolo_info.get('status', 'Unknown')}")
                    if yolo_info.get('sahi_enabled'):
                        print(f"      SAHI: Enabled (slice size: {yolo_info.get('sahi_slice_height', 'N/A')}x{yolo_info.get('sahi_slice_width', 'N/A')})")
                        print(f"      Method: {yolo_info.get('inference_method', 'N/A')}")
                else:
                    print(f"  🎯 YOLO: {yolo_info}")
                
                if isinstance(crowd_info, dict):
                    print(f"  🧠 Crowd Counter: {crowd_info.get('model_type', 'Unknown')}")
                    if crowd_info.get('csrnet_enabled'):
                        csrnet_status = crowd_info.get('csrnet_status', 'unknown')
                        print(f"      CSRNet: {csrnet_status}")
                        if 'csrnet_details' in crowd_info:
                            csrnet_details = crowd_info['csrnet_details']
                            print(f"      Method: {csrnet_details.get('description', 'N/A')}")
                else:
                    print(f"  🧠 Crowd Counter: {crowd_info}")
            
            # Summary statistics
            if 'details' in results and 'summary_statistics' in results['details']:
                stats = results['details']['summary_statistics']
                print(f"\n📈 Summary Statistics:")
                print(f"  Max count per frame: {stats.get('max_count_per_frame', 'N/A')}")
                print(f"  Min count per frame: {stats.get('min_count_per_frame', 'N/A')}")
                print(f"  Average density score: {stats.get('avg_density_score', 'N/A'):.3f}")
            
            # Sample frame analysis
            if is_tracking_based and 'frame_results' in results:
                frames = results['frame_results']
                print(f"\n🎬 Sample Tracking Frame Analysis:")

                # Show first, middle, and last frames
                sample_indices = [0, len(frames)//2, len(frames)-1] if len(frames) > 2 else range(len(frames))

                for i in sample_indices:
                    if i < len(frames):
                        frame = frames[i]
                        print(f"  Frame {frame['frame_number']} (t={frame.get('timestamp', 0):.1f}s):")
                        print(f"    Instant detections: {frame.get('instant_detections', 'N/A')} persons")
                        print(f"    Tracked persons: {frame.get('tracked_persons', 'N/A')} persons")
                        print(f"    Recommended count: {frame.get('recommended_count', 'N/A')} persons")
                        print(f"    Counting method: {frame.get('counting_method', 'N/A')}")
                        print(f"    Density score: {frame.get('density_score', 0):.3f}")
                        if frame.get('active_track_ids'):
                            print(f"    Active track IDs: {frame['active_track_ids'][:5]}{'...' if len(frame['active_track_ids']) > 5 else ''}")
            elif 'details' in results and 'frames' in results['details']:
                # Traditional frame analysis (fallback)
                frames = results['details']['frames']
                print(f"\n🎬 Sample Frame Analysis:")

                sample_indices = [0, len(frames)//2, len(frames)-1] if len(frames) > 2 else range(len(frames))

                for i in sample_indices:
                    if i < len(frames):
                        frame = frames[i]
                        print(f"  Frame {frame['frame_number']} (t={frame['timestamp']:.1f}s):")
                        print(f"    Detected: {frame['detections_count']} persons")
                        print(f"    Estimated: {frame.get('estimated_count', 'N/A')} persons")
                        print(f"    Density score: {frame['density_score']:.3f}")
                        print(f"    Method: {frame.get('method_used', 'N/A')}")
                        if frame['crowd_analysis']['distribution'] != 'empty':
                            print(f"    Distribution: {frame['crowd_analysis']['distribution']}")
            
            # Annotated frames
            if frame_urls:
                frame_type = "Tracked" if is_tracking_based else "Annotated"
                print(f"\n🖼️  Sample {frame_type} Frames:")
                for i, frame_url in enumerate(frame_urls[:5], 1):  # Show first 5
                    print(f"  {i}. http://localhost:8000{frame_url}")
                if len(frame_urls) > 5:
                    print(f"  ... and {len(frame_urls) - 5} more frames")
            
            print(f"\n🌐 Access Results:")
            print(f"  Web Interface: http://localhost:8000")
            print(f"  View all annotated frames in the static/ directory")
            
            # Performance metrics
            if is_tracking_based and 'video_summary' in results:
                summary = results['video_summary']
                total_frames = summary.get('total_frames_processed', 1)
            else:
                total_frames = results.get('total_frames_analyzed', 1)

            frames_per_second = total_frames / processing_time
            print(f"\n⚡ Performance Metrics:")
            print(f"  Processing speed: {frames_per_second:.2f} FPS")
            print(f"  Average time per frame: {processing_time / total_frames:.2f}s")

            if is_tracking_based and 'video_summary' in results:
                summary = results['video_summary']
                print(f"  🎯 Tracking efficiency: Unique persons vs total detections")
                unique_persons = summary.get('unique_persons_detected', 1)
                total_detections = summary.get('tracking_statistics', {}).get('total_detections', unique_persons)
                efficiency = (unique_persons / max(total_detections, 1)) * 100
                print(f"     Efficiency ratio: {efficiency:.1f}% ({unique_persons}/{total_detections})")
            
            return True
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error details: {error_detail}")
            except:
                print(f"Error response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out. The video is very large and processing is taking too long.")
        print("💡 Try with a shorter video clip or increase the timeout.")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    print("🎥 AI Crowd POC - Shibuya Crossing Analysis")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly. Please check if it's running.")
            return
    except:
        print("❌ Cannot connect to server. Please make sure it's running on http://localhost:8000")
        print("Run: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
        return
    
    print("✅ Server is running")
    
    # Test Shibuya video
    success = test_shibuya_video()
    
    if success:
        print("\n🎉 Shibuya crossing analysis completed successfully!")
        print("🗾 This demonstrates the system's capability to handle real-world crowd scenarios.")
        print("🎯 Key improvement: UNIQUE PERSON COUNTING eliminates double-counting across frames!")
        print("📊 The hybrid tracking approach adapts to crowd density for optimal accuracy.")
    else:
        print("\n❌ Analysis failed. Check the server logs for more details.")

if __name__ == "__main__":
    main()
