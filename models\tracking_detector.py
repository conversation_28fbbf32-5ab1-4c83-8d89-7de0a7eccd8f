"""
Enhanced person detector with tracking capabilities
Combines YOLO detection with SORT tracking for unique person counting
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from .yolo_detector import YOLOPersonDetector
from .sort_tracker import Sort

logger = logging.getLogger(__name__)

class TrackingPersonDetector:
    """
    Person detector with tracking capabilities for unique person counting
    """
    
    def __init__(self, 
                 model_name: str = 'yolov11s',
                 detection_preset: str = 'balanced',
                 sahi_preset: str = 'balanced',
                 use_sahi: bool = True,
                 max_age: int = 30,
                 min_hits: int = 3,
                 iou_threshold: float = 0.3):
        """
        Initialize tracking person detector
        
        Args:
            model_name: YOLO model to use
            detection_preset: Detection configuration preset
            sahi_preset: SAHI configuration preset
            use_sahi: Whether to use SAHI for detection
            max_age: Maximum number of frames to keep alive a track without associated detections
            min_hits: Minimum number of associated detections before track is initialized
            iou_threshold: Minimum IOU for match
        """
        # Initialize <PERSON><PERSON><PERSON> detector
        self.yolo_detector = YOLOPersonDetector(
            model_name=model_name,
            detection_preset=detection_preset,
            sahi_preset=sahi_preset,
            use_sahi=use_sahi
        )
        
        # Initialize SORT tracker
        self.tracker = Sort(
            max_age=max_age,
            min_hits=min_hits,
            iou_threshold=iou_threshold
        )
        
        # Tracking statistics
        self.frame_count = 0
        self.total_detections = 0
        self.unique_person_count = 0
        self.track_history = {}  # Store track history for analysis
        
        logger.info(f"Initialized TrackingPersonDetector with SORT tracker")
        logger.info(f"Tracker params: max_age={max_age}, min_hits={min_hits}, iou_threshold={iou_threshold}")
    
    def load_model(self):
        """Load the YOLO model"""
        self.yolo_detector.load_model()
    
    def detect_and_track(self, frame: np.ndarray) -> Dict:
        """
        Detect persons and update tracking
        
        Args:
            frame: Input frame as numpy array
            
        Returns:
            Dictionary with detection and tracking results
        """
        self.frame_count += 1
        
        # Get detections from YOLO
        detections = self.yolo_detector.detect_persons(frame)
        self.total_detections += len(detections)
        
        # Convert detections to SORT format [x1, y1, x2, y2, confidence]
        if len(detections) > 0:
            det_array = np.array([[
                det['bbox'][0], det['bbox'][1], det['bbox'][2], det['bbox'][3], det['confidence']
            ] for det in detections])
        else:
            det_array = np.empty((0, 5))
        
        # Update tracker
        tracked_objects = self.tracker.update(det_array)
        
        # Update unique person count
        self.unique_person_count = self.tracker.get_track_count()
        
        # Process tracking results
        tracked_detections = []
        active_track_ids = set()
        
        for track in tracked_objects:
            x1, y1, x2, y2, track_id = track
            track_id = int(track_id)
            active_track_ids.add(track_id)
            
            # Find corresponding detection (if any)
            corresponding_detection = None
            min_distance = float('inf')
            
            for det in detections:
                det_center = det['center']
                track_center = [(x1 + x2) / 2, (y1 + y2) / 2]
                distance = np.sqrt((det_center[0] - track_center[0])**2 + 
                                 (det_center[1] - track_center[1])**2)
                if distance < min_distance:
                    min_distance = distance
                    corresponding_detection = det
            
            # Create tracked detection
            tracked_det = {
                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                'center': [int((x1 + x2) / 2), int((y1 + y2) / 2)],
                'area': int((x2 - x1) * (y2 - y1)),
                'track_id': track_id,
                'confidence': corresponding_detection['confidence'] if corresponding_detection else 0.5,
                'class_id': 0,  # person class
                'class_name': 'person',
                'method': 'tracked'
            }
            
            tracked_detections.append(tracked_det)
            
            # Update track history
            if track_id not in self.track_history:
                self.track_history[track_id] = {
                    'first_seen': self.frame_count,
                    'last_seen': self.frame_count,
                    'total_frames': 1,
                    'positions': [tracked_det['center']]
                }
            else:
                self.track_history[track_id]['last_seen'] = self.frame_count
                self.track_history[track_id]['total_frames'] += 1
                self.track_history[track_id]['positions'].append(tracked_det['center'])
        
        # Get active tracks info
        active_tracks = self.tracker.get_active_tracks()
        
        return {
            'detections': detections,  # Raw YOLO detections
            'tracked_detections': tracked_detections,  # Detections with track IDs
            'active_tracks': active_tracks,  # Active track information
            'frame_count': self.frame_count,
            'detection_count': len(detections),
            'tracked_count': len(tracked_detections),
            'unique_person_count': self.unique_person_count,
            'total_detections': self.total_detections,
            'active_track_ids': list(active_track_ids)
        }
    
    def annotate_frame_with_tracking(self, frame: np.ndarray, tracking_result: Dict) -> np.ndarray:
        """
        Annotate frame with tracking information
        
        Args:
            frame: Input frame
            tracking_result: Result from detect_and_track
            
        Returns:
            Annotated frame
        """
        annotated_frame = frame.copy()
        
        # Draw tracked detections
        for det in tracking_result['tracked_detections']:
            x1, y1, x2, y2 = det['bbox']
            track_id = det['track_id']
            confidence = det['confidence']
            
            # Choose color based on track ID
            colors = [
                (255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0),
                (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0)
            ]
            color = colors[track_id % len(colors)]
            
            # Draw bounding box
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
            
            # Draw track ID and confidence
            label = f"ID:{track_id} ({confidence:.2f})"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            cv2.putText(annotated_frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Draw center point
            center = det['center']
            cv2.circle(annotated_frame, tuple(center), 3, color, -1)
        
        # Add tracking statistics
        stats_text = [
            f"Frame: {tracking_result['frame_count']}",
            f"Current Detections: {tracking_result['detection_count']}",
            f"Active Tracks: {tracking_result['tracked_count']}",
            f"Unique Persons: {tracking_result['unique_person_count']}"
        ]
        
        y_offset = 30
        for i, text in enumerate(stats_text):
            cv2.putText(annotated_frame, text, (10, y_offset + i * 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        return annotated_frame
    
    def get_tracking_statistics(self) -> Dict:
        """
        Get comprehensive tracking statistics
        
        Returns:
            Dictionary with tracking statistics
        """
        active_tracks = len([tid for tid, info in self.track_history.items() 
                           if info['last_seen'] >= self.frame_count - 5])
        
        # Calculate average track duration
        track_durations = [info['total_frames'] for info in self.track_history.values()]
        avg_duration = np.mean(track_durations) if track_durations else 0
        
        return {
            'total_frames_processed': self.frame_count,
            'total_detections': self.total_detections,
            'unique_person_count': self.unique_person_count,
            'active_tracks': active_tracks,
            'average_track_duration': avg_duration,
            'track_history_count': len(self.track_history),
            'avg_detections_per_frame': self.total_detections / max(1, self.frame_count)
        }
    
    def reset_tracking(self):
        """Reset tracking state"""
        self.tracker.reset()
        self.frame_count = 0
        self.total_detections = 0
        self.unique_person_count = 0
        self.track_history = {}
        logger.info("Tracking state reset")
    
    def get_config_info(self) -> Dict:
        """Get configuration information"""
        config = self.yolo_detector.get_config_info()
        config.update({
            'tracking_enabled': True,
            'tracker_type': 'SORT',
            'max_age': self.tracker.max_age,
            'min_hits': self.tracker.min_hits,
            'iou_threshold': self.tracker.iou_threshold
        })
        return config
